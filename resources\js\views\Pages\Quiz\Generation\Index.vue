<template>
    <div>
        <PageHeader
            :title="$trans('quiz.generation.generate_quiz')"
            :navs="[
                { label: $trans('quiz.quiz'), path: 'QuizList' },
            ]"
        />

        <ParentTransition appear :visibility="true">
            <div class="container-xl">
                <div class="row">
                    <!-- Source Selection -->
                    <div class="col-lg-8">
                        <BaseCard>
                            <template #header>
                                <CardHeader
                                    :title="$trans('quiz.generation.ai_generation')"
                                    icon="fas fa-magic"
                                />
                            </template>

                            <!-- Source Type Selection -->
                            <div class="mb-4">
                                <h6 class="mb-3">{{ $trans('quiz.generation.content_source') }}</h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div 
                                            class="source-card"
                                            :class="{ active: selectedSource === 'text' }"
                                            @click="selectSource('text')"
                                        >
                                            <div class="source-icon">
                                                <i class="fas fa-file-text"></i>
                                            </div>
                                            <div class="source-content">
                                                <h6>{{ $trans('quiz.generation.source_types.text') }}</h6>
                                                <p class="text-muted small">{{ $trans('quiz.generation.enter_text_content') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div 
                                            class="source-card"
                                            :class="{ active: selectedSource === 'topic' }"
                                            @click="selectSource('topic')"
                                        >
                                            <div class="source-icon">
                                                <i class="fas fa-lightbulb"></i>
                                            </div>
                                            <div class="source-content">
                                                <h6>{{ $trans('quiz.generation.source_types.topic') }}</h6>
                                                <p class="text-muted small">{{ $trans('quiz.generation.enter_topic') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div 
                                            class="source-card"
                                            :class="{ active: selectedSource === 'url' }"
                                            @click="selectSource('url')"
                                        >
                                            <div class="source-icon">
                                                <i class="fas fa-link"></i>
                                            </div>
                                            <div class="source-content">
                                                <h6>{{ $trans('quiz.generation.source_types.url') }}</h6>
                                                <p class="text-muted small">{{ $trans('quiz.generation.enter_url') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div 
                                            class="source-card"
                                            :class="{ active: selectedSource === 'document' }"
                                            @click="selectSource('document')"
                                        >
                                            <div class="source-icon">
                                                <i class="fas fa-file-upload"></i>
                                            </div>
                                            <div class="source-content">
                                                <h6>{{ $trans('quiz.generation.source_types.document') }}</h6>
                                                <p class="text-muted small">{{ $trans('quiz.generation.upload_document') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Content Input Area -->
                            <div v-if="selectedSource" class="content-input-area">
                                <!-- Text Content -->
                                <div v-if="selectedSource === 'text'" class="mb-4">
                                    <BaseTextarea
                                        v-model="formData.content"
                                        :label="$trans('quiz.generation.source_types.text')"
                                        :placeholder="$trans('quiz.generation.placeholders.content')"
                                        rows="8"
                                        required
                                        :error="errors.content"
                                    />
                                    <small class="text-muted">{{ $trans('quiz.generation.hints.content_length') }}</small>
                                </div>

                                <!-- Topic Input -->
                                <div v-if="selectedSource === 'topic'" class="mb-4">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <BaseInput
                                                v-model="formData.topic"
                                                :label="$trans('ai.academic.topic')"
                                                :placeholder="$trans('quiz.generation.placeholders.topic')"
                                                required
                                                :error="errors.topic"
                                            />
                                        </div>
                                        <div class="col-md-6">
                                            <BaseSelect
                                                v-model="formData.subject"
                                                :label="$trans('ai.knowledge.props.subject')"
                                                :options="subjectOptions"
                                                :placeholder="$trans('quiz.generation.placeholders.subject')"
                                                :error="errors.subject"
                                            />
                                        </div>
                                    </div>
                                    <BaseInput
                                        v-model="formData.level"
                                        :label="$trans('ai.knowledge.props.level')"
                                        :placeholder="$trans('quiz.generation.placeholders.level')"
                                        :error="errors.level"
                                    />
                                </div>

                                <!-- URL Input -->
                                <div v-if="selectedSource === 'url'" class="mb-4">
                                    <BaseInput
                                        v-model="formData.url"
                                        :label="$trans('quiz.generation.source_types.url')"
                                        :placeholder="$trans('quiz.generation.placeholders.url')"
                                        type="url"
                                        required
                                        :error="errors.url"
                                    />
                                </div>

                                <!-- Document Upload -->
                                <div v-if="selectedSource === 'document'" class="mb-4">
                                    <BaseFileUpload
                                        v-model="formData.document"
                                        :label="$trans('quiz.generation.source_types.document')"
                                        accept=".pdf,.doc,.docx,.txt"
                                        :error="errors.document"
                                    />
                                </div>

                                <!-- Generation Settings -->
                                <div class="generation-settings">
                                    <h6 class="mb-3">{{ $trans('quiz.generation.generation_settings') }}</h6>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <BaseInput
                                                v-model="formData.title"
                                                :label="$trans('quiz.props.title')"
                                                :placeholder="$trans('quiz.generation.placeholders.title')"
                                                :error="errors.title"
                                            />
                                        </div>
                                        <div class="col-md-6">
                                            <BaseSelect
                                                v-model="formData.categoryId"
                                                :label="$trans('quiz.props.category')"
                                                :options="categoryOptions"
                                                :placeholder="$trans('general.select')"
                                                :error="errors.categoryId"
                                            />
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <BaseSelect
                                                v-model="formData.difficulty"
                                                :label="$trans('quiz.generation.difficulty_level')"
                                                :options="difficultyOptions"
                                                required
                                                :error="errors.difficulty"
                                            />
                                        </div>
                                        <div class="col-md-4">
                                            <BaseInput
                                                v-model="formData.questionCount"
                                                :label="$trans('ai.academic.question_count')"
                                                type="number"
                                                min="1"
                                                max="50"
                                                required
                                                :error="errors.questionCount"
                                            />
                                        </div>
                                        <div class="col-md-4">
                                            <BaseMultiSelect
                                                v-model="formData.questionTypes"
                                                :label="$trans('quiz.generation.question_types_selection')"
                                                :options="questionTypeOptions"
                                                required
                                                :error="errors.questionTypes"
                                            />
                                        </div>
                                    </div>

                                    <small class="text-muted">{{ $trans('quiz.generation.hints.question_count') }}</small>
                                </div>

                                <!-- Generate Button -->
                                <div class="mt-4 d-flex justify-content-end">
                                    <BaseButton
                                        @click="generateQuiz"
                                        design="primary"
                                        size="lg"
                                        :disabled="isGenerating || !canGenerate"
                                    >
                                        <i v-if="isGenerating" class="fas fa-spinner fa-spin me-2"></i>
                                        <i v-else class="fas fa-magic me-2"></i>
                                        {{ $trans('ai.academic.generate') }}
                                    </BaseButton>
                                </div>
                            </div>
                        </BaseCard>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- Prerequisites -->
                        <BaseCard class="mb-4">
                            <template #header>
                                <CardHeader
                                    :title="$trans('quiz.generation.prerequisites')"
                                    icon="fas fa-info-circle"
                                />
                            </template>

                            <div class="prerequisites-list">
                                <div class="prerequisite-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>{{ $trans('quiz.generation.select_source_type') }}</span>
                                </div>
                                <div class="prerequisite-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>{{ $trans('quiz.generation.generation_settings') }}</span>
                                </div>
                                <div class="prerequisite-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>{{ $trans('quiz.generation.quiz_settings') }}</span>
                                </div>
                            </div>
                        </BaseCard>

                        <!-- Recent Generations -->
                        <BaseCard v-if="recentGenerations.length > 0">
                            <template #header>
                                <CardHeader
                                    :title="$trans('ai.conversation.recent_conversations')"
                                    icon="fas fa-history"
                                />
                            </template>

                            <div class="recent-generations">
                                <div 
                                    v-for="generation in recentGenerations" 
                                    :key="generation.id"
                                    class="recent-item"
                                    @click="viewGeneration(generation)"
                                >
                                    <div class="recent-icon">
                                        <i class="fas fa-question-circle"></i>
                                    </div>
                                    <div class="recent-content">
                                        <h6>{{ generation.title }}</h6>
                                        <small class="text-muted">{{ $cal.toUserTimezone(generation.createdAt, 'date') }}</small>
                                    </div>
                                </div>
                            </div>
                        </BaseCard>
                    </div>
                </div>
            </div>
        </ParentTransition>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, inject } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { useStore } from 'vuex'
import { trans } from '@core/helpers/trans'

// Composables
const router = useRouter()
const toast = useToast()
const $cal = inject("$cal")
const store = useStore()

// Reactive data
const selectedSource = ref('')
const isGenerating = ref(false)
const errors = ref({})
const recentGenerations = ref([])

const formData = reactive({
    content: '',
    topic: '',
    subject: '',
    level: '',
    url: '',
    document: null,
    title: '',
    categoryId: '',
    difficulty: 'intermediate',
    questionCount: 10,
    questionTypes: ['mcq', 'true_false'],
})

// Computed properties
const canGenerate = computed(() => {
    if (!selectedSource.value) return false
    
    switch (selectedSource.value) {
        case 'text':
            return formData.content.length >= 100
        case 'topic':
            return formData.topic.trim() !== ''
        case 'url':
            return formData.url.trim() !== ''
        case 'document':
            return formData.document !== null
        default:
            return false
    }
})

const difficultyOptions = computed(() => [
    { value: 'basic', label: trans('quiz.difficulty.basic') },
    { value: 'intermediate', label: trans('quiz.difficulty.intermediate') },
    { value: 'advanced', label: trans('quiz.difficulty.advanced') },
])

const questionTypeOptions = computed(() => [
    { value: 'mcq', label: trans('quiz.question_types.multiple_choice') },
    { value: 'true_false', label: trans('quiz.question_types.true_false') },
    { value: 'short_answer', label: trans('quiz.question_types.short_answer') },
    { value: 'essay', label: trans('quiz.question_types.essay') },
])

const subjectOptions = computed(() => store.getters['option/getOptions']('subjects'))
const categoryOptions = computed(() => store.getters['quiz/category/getOptions']())

// Methods
const selectSource = (source) => {
    selectedSource.value = source
    errors.value = {}
}

const generateQuiz = async () => {
    if (!canGenerate.value) return
    
    isGenerating.value = true
    errors.value = {}
    
    try {
        const endpoint = `quiz/generate/${selectedSource.value}`
        const response = await store.dispatch('quiz/generation/generate', {
            endpoint,
            data: formData,
        })
        
        toast.success(trans(`quiz.generation.success.${selectedSource.value}`))
        
        // Redirect to the created quiz
        router.push({ name: 'QuizShow', params: { uuid: response.quiz.uuid } })
    } catch (error) {
        if (error.response?.data?.errors) {
            errors.value = error.response.data.errors
        }
        toast.error(error.message || trans(`quiz.generation.error.${selectedSource.value}`))
    } finally {
        isGenerating.value = false
    }
}

const viewGeneration = (generation) => {
    router.push({ name: 'QuizShow', params: { uuid: generation.uuid } })
}

// Removed custom formatDate - using $cal.toUserTimezone instead

// Lifecycle
onMounted(async () => {
    await store.dispatch('option/getOptions', 'subjects')
    await store.dispatch('quiz/category/fetch')
})
</script>

<style scoped>
.source-card {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    height: 100%;
    display: flex;
    align-items: center;
}

.source-card:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.source-card.active {
    border-color: #007bff;
    background-color: #e7f3ff;
}

.source-icon {
    font-size: 2rem;
    color: #6c757d;
    margin-right: 1rem;
}

.source-card.active .source-icon {
    color: #007bff;
}

.source-content h6 {
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.content-input-area {
    border-top: 1px solid #e9ecef;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
}

.generation-settings {
    border-top: 1px solid #e9ecef;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
}

.prerequisite-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.recent-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.recent-item:hover {
    background-color: #f8f9fa;
}

.recent-icon {
    font-size: 1.25rem;
    color: #6c757d;
    margin-right: 0.75rem;
}

.recent-content h6 {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}
</style>
