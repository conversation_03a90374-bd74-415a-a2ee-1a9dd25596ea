<?php

namespace App\Http\Resources\Quiz;

use App\Http\Resources\Student\StudentResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StudentQuizCollectionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'uuid' => $this->uuid,
            'name' => $this->name,
            'description' => $this->description,
            'color' => $this->color,
            'isDefault' => $this->is_default,
            'totalQuizzes' => $this->total_quizzes,
            'student' => $this->whenLoaded('student', function () {
                return StudentResource::make($this->student);
            }),
            'items' => $this->whenLoaded('items', function () {
                return QuizCollectionItemResource::collection($this->items);
            }),
            'meta' => $this->meta,
            'createdAt' => $this->created_at,
            'updatedAt' => $this->updated_at,
        ];
    }
}
