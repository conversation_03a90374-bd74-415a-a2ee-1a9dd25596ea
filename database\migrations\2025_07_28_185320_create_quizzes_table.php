<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quizzes', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->index()->unique();

            // Foreign keys following existing patterns
            $table->foreignId('period_id')->nullable()->constrained('periods')->onDelete('cascade');
            $table->foreignId('team_id')->nullable()->constrained('teams')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->foreignId('category_id')->nullable()->constrained('quiz_categories')->onDelete('set null');

            // Basic information
            $table->string('title', 200)->nullable();
            $table->text('description')->nullable();
            $table->text('instructions')->nullable();

            // Quiz configuration
            $table->enum('quiz_type', ['personal', 'educational', 'public'])->default('educational');
            $table->enum('creator_type', ['student', 'teacher', 'admin'])->default('teacher');
            $table->enum('difficulty_level', ['easy', 'medium', 'hard'])->default('medium');

            // Access control
            $table->boolean('is_public')->default(false);
            $table->enum('public_access_type', ['open', 'registration_required', 'pre_approval'])->default('open');
            $table->string('unique_code', 20)->nullable()->unique();
            $table->boolean('require_registration')->default(false);
            $table->boolean('require_approval')->default(false);
            $table->boolean('show_correct_answers')->default(true);
            $table->integer('passing_score')->default(60);

            // Date restrictions
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();

            // Attempt control
            $table->integer('max_attempts_student')->default(0)->comment('0 = unlimited');
            $table->integer('max_attempts_public')->default(1);
            $table->integer('max_attempts')->default(0)->comment('0 = unlimited, for public quizzes');
            $table->enum('attempt_reset_period', ['never', 'daily', 'weekly', 'monthly'])->default('never');

            // Sharing settings
            $table->boolean('allow_peer_sharing')->default(true);
            $table->boolean('allow_result_sharing')->default(true);
            $table->boolean('discovery_enabled')->default(false);

            // Timing
            $table->integer('time_limit')->nullable()->comment('minutes');

            // Status and metadata
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
            $table->timestamp('published_at')->nullable();

            // Standard fields
            $table->json('config')->nullable();
            $table->json('meta')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quizzes');
    }
};
