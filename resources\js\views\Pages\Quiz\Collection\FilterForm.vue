<template>
    <FilterForm
        :init-form="initForm"
        :form="form"
        :multiple="[]"
        @hide="emit('hide')"
    >
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Visibility Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.collection.visibility') }}
                </label>
                <select
                    v-model="form.visibility"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="private">{{ $trans('quiz.visibility.private') }}</option>
                    <option value="shared">{{ $trans('quiz.visibility.shared') }}</option>
                    <option value="public">{{ $trans('quiz.visibility.public') }}</option>
                </select>
            </div>

            <!-- Created By Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.collection.created_by') }}
                </label>
                <select
                    v-model="form.createdBy"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="me">{{ $trans('quiz.collection.my_collections') }}</option>
                    <option 
                        v-for="user in availableUsers" 
                        :key="user.uuid" 
                        :value="user.uuid"
                    >
                        {{ user.name }}
                    </option>
                </select>
            </div>

            <!-- Quiz Count Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.collection.quiz_count') }}
                </label>
                <select
                    v-model="form.quizCount"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="empty">{{ $trans('quiz.collection.empty_collections') }}</option>
                    <option value="1-5">1-5 {{ $trans('quiz.quizzes') }}</option>
                    <option value="6-10">6-10 {{ $trans('quiz.quizzes') }}</option>
                    <option value="11-20">11-20 {{ $trans('quiz.quizzes') }}</option>
                    <option value="20+">20+ {{ $trans('quiz.quizzes') }}</option>
                </select>
            </div>

            <!-- Featured Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.collection.featured') }}
                </label>
                <select
                    v-model="form.featured"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="1">{{ $trans('quiz.collection.featured_only') }}</option>
                    <option value="0">{{ $trans('quiz.collection.not_featured') }}</option>
                </select>
            </div>

            <!-- Date Range Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.collection.created_from') }}
                </label>
                <input
                    v-model="form.createdFrom"
                    type="date"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.collection.created_to') }}
                </label>
                <input
                    v-model="form.createdTo"
                    type="date"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
            </div>

            <!-- Tags Filter -->
            <div class="md:col-span-2 lg:col-span-3">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.collection.tags') }}
                </label>
                <input
                    v-model="form.tags"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="$trans('quiz.collection.search_by_tags')"
                />
            </div>

            <!-- Search Filter -->
            <div class="md:col-span-2 lg:col-span-3">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('global.search') }}
                </label>
                <input
                    v-model="form.search"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="$trans('quiz.collection.search_collections')"
                />
            </div>
        </div>
    </FilterForm>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue"
import { useRoute } from "vue-router"

const route = useRoute()

const emit = defineEmits(["hide"])

const availableUsers = ref([])

const initForm = {
    visibility: "",
    createdBy: "",
    quizCount: "",
    featured: "",
    createdFrom: "",
    createdTo: "",
    tags: "",
    search: "",
}

const form = reactive({ ...initForm })

const fetchData = reactive({
    isLoaded: true,
})

onMounted(async () => {
    fetchData.isLoaded = true
})
</script>
