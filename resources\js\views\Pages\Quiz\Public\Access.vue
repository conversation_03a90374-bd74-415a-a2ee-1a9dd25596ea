<template>
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <div class="text-center">
                <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                    {{ $trans('quiz.public_quiz_access') }}
                </h2>
                <p class="mt-2 text-sm text-gray-600" v-if="quiz.title">
                    {{ quiz.title }}
                </p>
            </div>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <!-- Loading State -->
                <div v-if="loading" class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p class="mt-4 text-gray-600">{{ $trans('quiz.loading_quiz') }}</p>
                </div>

                <!-- Quiz Not Found -->
                <div v-else-if="!quiz.uuid" class="text-center">
                    <div class="text-red-500 text-6xl mb-4">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">
                        {{ $trans('quiz.quiz_not_found') }}
                    </h3>
                    <p class="text-gray-600">
                        {{ $trans('quiz.invalid_quiz_code') }}
                    </p>
                </div>

                <!-- Quiz Access Denied -->
                <div v-else-if="!canAccess" class="text-center">
                    <div class="text-yellow-500 text-6xl mb-4">
                        <i class="fas fa-lock"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">
                        {{ $trans('quiz.access_denied') }}
                    </h3>
                    <p class="text-gray-600 mb-4">
                        {{ accessMessage }}
                    </p>
                </div>

                <!-- Quiz Available -->
                <div v-else class="space-y-6">
                    <!-- Quiz Information -->
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="font-medium text-blue-900 mb-2">{{ quiz.title }}</h3>
                        <p class="text-blue-700 text-sm" v-if="quiz.description">
                            {{ quiz.description }}
                        </p>
                        
                        <div class="mt-4 grid grid-cols-2 gap-4 text-sm">
                            <div v-if="quiz.timeLimit">
                                <span class="text-blue-600">{{ $trans('quiz.time_limit') }}:</span>
                                <span class="font-medium ml-1">{{ quiz.timeLimit }} {{ $trans('quiz.minutes') }}</span>
                            </div>
                            <div v-if="quiz.questionCount">
                                <span class="text-blue-600">{{ $trans('quiz.questions') }}:</span>
                                <span class="font-medium ml-1">{{ quiz.questionCount }}</span>
                            </div>
                            <div v-if="quiz.passingScore">
                                <span class="text-blue-600">{{ $trans('quiz.passing_score') }}:</span>
                                <span class="font-medium ml-1">{{ quiz.passingScore }}%</span>
                            </div>
                            <div v-if="quiz.maxAttempts">
                                <span class="text-blue-600">{{ $trans('quiz.max_attempts') }}:</span>
                                <span class="font-medium ml-1">{{ quiz.maxAttempts }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Previous Attempts -->
                    <div v-if="previousAttempts.length > 0" class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-gray-900 mb-3">{{ $trans('quiz.previous_attempts') }}</h4>
                        <div class="space-y-2">
                            <div 
                                v-for="attempt in previousAttempts" 
                                :key="attempt.uuid"
                                class="flex justify-between items-center text-sm"
                            >
                                <span>{{ $cal.toUserTimezone(attempt.completedAt, 'date') }}</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-medium" :class="getScoreColor(attempt.score)">
                                        {{ attempt.score }}%
                                    </span>
                                    <button
                                        @click="viewResult(attempt.uuid)"
                                        class="text-blue-600 hover:text-blue-800"
                                    >
                                        {{ $trans('quiz.view_result') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <!-- Registration Required -->
                        <button
                            v-if="quiz.requireRegistration && !isRegistered"
                            @click="goToRegistration"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            {{ $trans('quiz.register_to_take_quiz') }}
                        </button>

                        <!-- Start Quiz -->
                        <button
                            v-else-if="canStartQuiz"
                            @click="startQuiz"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                            {{ $trans('quiz.start_quiz') }}
                        </button>

                        <!-- Max Attempts Reached -->
                        <div v-else-if="maxAttemptsReached" class="text-center">
                            <p class="text-red-600 text-sm">
                                {{ $trans('quiz.max_attempts_reached') }}
                            </p>
                        </div>
                    </div>

                    <!-- Quiz Instructions -->
                    <div v-if="quiz.instructions" class="bg-yellow-50 p-4 rounded-lg">
                        <h4 class="font-medium text-yellow-900 mb-2">{{ $trans('quiz.instructions') }}</h4>
                        <div class="text-yellow-800 text-sm" v-html="quiz.instructions"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "QuizPublicAccess",
}
</script>

<script setup>
import { reactive, ref, computed, onMounted, inject } from "vue"
import { useRoute, useRouter } from "vue-router"
import { useStore } from "vuex"

const route = useRoute()
const router = useRouter()
const store = useStore()
const $cal = inject("$cal")

const loading = ref(true)
const quiz = reactive({})
const previousAttempts = ref([])
const isRegistered = ref(false)

const canAccess = computed(() => {
    if (!quiz.uuid) return false
    if (quiz.startDate && new Date(quiz.startDate) > new Date()) return false
    if (quiz.endDate && new Date(quiz.endDate) < new Date()) return false
    return true
})

const accessMessage = computed(() => {
    if (quiz.startDate && new Date(quiz.startDate) > new Date()) {
        return `Quiz will be available from ${$cal.toUserTimezone(quiz.startDate, 'date')}`
    }
    if (quiz.endDate && new Date(quiz.endDate) < new Date()) {
        return `Quiz ended on ${$cal.toUserTimezone(quiz.endDate, 'date')}`
    }
    return 'Access denied'
})

const maxAttemptsReached = computed(() => {
    return quiz.maxAttempts && previousAttempts.value.length >= quiz.maxAttempts
})

const canStartQuiz = computed(() => {
    return !maxAttemptsReached.value && (!quiz.requireRegistration || isRegistered.value)
})

const fetchQuizData = async () => {
    try {
        loading.value = true

        // Fetch public quiz data
        const response = await store.dispatch('quiz/getPublicQuiz', route.params.code)
        Object.assign(quiz, response.quiz)

        // Check if user is already registered
        if (quiz.requireRegistration) {
            await checkRegistrationStatus()
        }

        // Fetch previous attempts if any
        await fetchPreviousAttempts()

    } catch (error) {
        console.error('Error fetching quiz data:', error)
        // Quiz not found or access denied
        quiz.uuid = null
    } finally {
        loading.value = false
    }
}

const checkRegistrationStatus = async () => {
    try {
        const email = localStorage.getItem(`quiz_${route.params.code}_email`)
        const token = localStorage.getItem(`quiz_${route.params.code}_token`)

        if (email && token) {
            const response = await store.dispatch('quiz/checkParticipantStatus', {
                code: route.params.code,
                email: email,
                token: token
            })
            isRegistered.value = response.participant?.status === 'approved'
        }
    } catch (error) {
        console.error('Error checking registration status:', error)
    }
}

const fetchPreviousAttempts = async () => {
    try {
        const email = localStorage.getItem(`quiz_${route.params.code}_email`)
        if (email) {
            const response = await store.dispatch('quiz/getPublicAttempts', {
                code: route.params.code,
                email: email
            })
            previousAttempts.value = response.attempts || []
        }
    } catch (error) {
        console.error('Error fetching previous attempts:', error)
        previousAttempts.value = []
    }
}

const goToRegistration = () => {
    router.push({
        name: 'QuizPublicRegister',
        params: { code: route.params.code }
    })
}

const startQuiz = () => {
    router.push({
        name: 'QuizPublicTake',
        params: { code: route.params.code }
    })
}

const viewResult = (attemptUuid) => {
    router.push({
        name: 'QuizPublicResult',
        params: { 
            code: route.params.code,
            attemptUuid: attemptUuid
        }
    })
}

// Removed custom formatDate - using $cal.toUserTimezone instead

const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
}

onMounted(() => {
    fetchQuizData()
})
</script>
