<template>
    <FilterForm
        :init-form="initForm"
        :form="form"
        :multiple="['categories']"
        @hide="emit('hide')"
    >
        <div class="grid grid-cols-3 gap-6">
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.categoryUuid"
                    name="categoryUuid"
                    :label="$trans('quiz.category.category')"
                    :options="preRequisites.categories"
                    label-prop="name"
                    value-prop="uuid"
                    clearable
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.difficulty"
                    name="difficulty"
                    :label="$trans('quiz.difficulty.difficulty')"
                    :options="difficultyOptions"
                    clearable
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.isPublished"
                    name="isPublished"
                    :label="$trans('quiz.status')"
                    :options="statusOptions"
                    clearable
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.isShared"
                    name="isShared"
                    :label="$trans('quiz.sharing')"
                    :options="sharingOptions"
                    clearable
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.createdBy"
                    name="createdBy"
                    :label="$trans('quiz.created_by')"
                    :options="createdByOptions"
                    clearable
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseInput
                    type="number"
                    v-model="form.minQuestions"
                    name="minQuestions"
                    :label="$trans('quiz.min_questions')"
                    min="0"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseInput
                    type="number"
                    v-model="form.maxQuestions"
                    name="maxQuestions"
                    :label="$trans('quiz.max_questions')"
                    min="0"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseInput
                    type="number"
                    v-model="form.minTimeLimit"
                    name="minTimeLimit"
                    :label="$trans('quiz.min_time_limit')"
                    min="0"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseInput
                    type="number"
                    v-model="form.maxTimeLimit"
                    name="maxTimeLimit"
                    :label="$trans('quiz.max_time_limit')"
                    min="0"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <DatePicker
                    v-model="form.createdAfter"
                    name="createdAfter"
                    :label="$trans('quiz.created_after')"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <DatePicker
                    v-model="form.createdBefore"
                    name="createdBefore"
                    :label="$trans('quiz.created_before')"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseInput
                    type="text"
                    v-model="form.keyword"
                    name="keyword"
                    :label="$trans('quiz.keyword')"
                    :placeholder="$trans('quiz.search_placeholder')"
                />
            </div>
        </div>
    </FilterForm>
</template>

<script setup>
import { reactive, computed, onMounted } from "vue"
import { useRoute } from "vue-router"

const route = useRoute()

const props = defineProps({
    preRequisites: {
        type: Object,
        default: () => ({}),
    },
})

const emit = defineEmits(["hide"])

const initForm = {
    categoryUuid: "",
    difficulty: "",
    isPublished: "",
    isShared: "",
    createdBy: "",
    minQuestions: "",
    maxQuestions: "",
    minTimeLimit: "",
    maxTimeLimit: "",
    createdAfter: "",
    createdBefore: "",
    keyword: "",
}

const form = reactive({ ...initForm })

const fetchData = reactive({
    isLoaded: true,
})

const difficultyOptions = computed(() => [
    { label: 'Basic', value: 'basic' },
    { label: 'Intermediate', value: 'intermediate' },
    { label: 'Advanced', value: 'advanced' },
])

const statusOptions = computed(() => [
    { label: 'Published', value: '1' },
    { label: 'Draft', value: '0' },
])

const sharingOptions = computed(() => [
    { label: 'Shared', value: '1' },
    { label: 'Not Shared', value: '0' },
])

const createdByOptions = computed(() => [
    { label: 'Created by Me', value: 'me' },
    { label: 'Created by Others', value: 'others' },
])

onMounted(async () => {
    fetchData.isLoaded = true
})
</script>
