<?php

return [
    'quiz' => 'Quiz',
    'quizzes' => 'Quizzes',
    'props' => [
        'title' => 'Title',
        'description' => 'Description',
        'category' => 'Category',
        'quiz_type' => 'Quiz Type',
        'creator_type' => 'Creator Type',
        'status' => 'Status',
        'is_public' => 'Public Quiz',
        'allow_peer_sharing' => 'Allow Peer Sharing',
        'allow_result_sharing' => 'Allow Result Sharing',
        'discovery_enabled' => 'Enable Discovery',
        'unique_code' => 'Unique Code',
        'published_at' => 'Published At',
        'question_count' => 'Question Count',
        'total_points' => 'Total Points',
        'time_limit' => 'Time Limit (minutes)',
        'attempt_limit' => 'Attempt Limit',
        'auto_grading' => 'Auto Grading',
        'show_results' => 'Show Results',
        'randomize_questions' => 'Randomize Questions',
        'randomize_options' => 'Randomize Options',
    ],

    'quiz_types' => [
        'practice' => 'Practice Quiz',
        'assessment' => 'Assessment Quiz',
        'ai_generated' => 'AI Generated Quiz',
        'imported' => 'Imported Quiz',
    ],

    'creator_types' => [
        'teacher' => 'Teacher',
        'student' => 'Student',
        'admin' => 'Administrator',
    ],

    'status' => [
        'draft' => 'Draft',
        'published' => 'Published',
        'archived' => 'Archived',
    ],

    'difficulty' => [
        'basic' => 'Basic',
        'intermediate' => 'Intermediate',
        'advanced' => 'Advanced',
    ],

    'question_types' => [
        'multiple_choice' => 'Multiple Choice',
        'true_false' => 'True/False',
        'short_answer' => 'Short Answer',
        'essay' => 'Essay',
    ],

    'category' => [
        'category' => 'Category',
        'categories' => 'Categories',
        'props' => [
            'name' => 'Name',
            'description' => 'Description',
            'color' => 'Color',
            'is_active' => 'Active',
        ],
    ],

    'question' => [
        'question' => 'Question',
        'questions' => 'Questions',
        'props' => [
            'question' => 'Question Text',
            'question_type' => 'Question Type',
            'options' => 'Options',
            'correct_answer' => 'Correct Answer',
            'explanation' => 'Explanation',
            'points' => 'Points',
            'order_sequence' => 'Order',
            'difficulty' => 'Difficulty',
        ],
        'add_question' => 'Add Question',
        'edit_question' => 'Edit Question',
        'delete_question' => 'Delete Question',
        'option_placeholder' => 'Enter option text',
        'add_option' => 'Add Option',
        'remove_option' => 'Remove Option',
    ],

    'attempt' => [
        'attempt' => 'Attempt',
        'attempts' => 'Attempts',
        'props' => [
            'started_at' => 'Started At',
            'completed_at' => 'Completed At',
            'score' => 'Score',
            'percentage' => 'Percentage',
            'time_taken' => 'Time Taken',
            'status' => 'Status',
            'answers' => 'Answers',
        ],
        'status' => [
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'abandoned' => 'Abandoned',
            'expired' => 'Expired',
        ],
        'start_quiz' => 'Start Quiz',
        'continue' => 'Continue Quiz',
        'submit_quiz' => 'Submit Quiz',
        'abandon' => 'Abandon Quiz',
        'save_progress' => 'Save Progress',
        'view_result' => 'View Result',
        'retake_quiz' => 'Retake Quiz',
        'export' => 'Export Results',
        'participant_name' => 'Participant Name',
        'participant_email' => 'Participant Email',
        'current_question' => 'Current Question',
        'attempt_number' => 'Attempt Number',
        'score_overview' => 'Score Overview',
        'continue_existing' => 'You have an existing attempt in progress',
        'max_attempts_reached' => 'Maximum attempts reached for this quiz',
        'started' => 'Quiz attempt started successfully',
        'progress_saved' => 'Progress saved successfully',
        'submitted' => 'Quiz submitted successfully',
        'abandoned' => 'Quiz attempt abandoned',
        'deleted' => 'Quiz attempt deleted successfully',
    ],

    'generation' => [
        'generate_quiz' => 'Generate Quiz',
        'ai_generation' => 'AI Quiz Generation',
        'prerequisites' => 'Generation Prerequisites',
        'source_types' => [
            'text' => 'Text Content',
            'topic' => 'Topic/Subject',
            'url' => 'Web URL',
            'document' => 'Document Upload',
        ],
        'content_source' => 'Content Source',
        'select_source_type' => 'Select Source Type',
        'enter_text_content' => 'Enter or paste your text content',
        'enter_topic' => 'Enter topic or subject',
        'enter_url' => 'Enter web URL',
        'upload_document' => 'Upload document (PDF, Word, Text)',
        'generation_settings' => 'Generation Settings',
        'difficulty_level' => 'Difficulty Level',
        'question_types_selection' => 'Question Types',
        'select_question_types' => 'Select question types to include',
        'quiz_settings' => 'Quiz Settings',
        'success' => [
            'text' => 'Quiz generated successfully from text content!',
            'topic' => 'Quiz generated successfully from topic!',
            'url' => 'Quiz generated successfully from web content!',
            'document' => 'Quiz generated successfully from document!',
        ],
        'error' => [
            'text' => 'Failed to generate quiz from text content.',
            'topic' => 'Failed to generate quiz from topic.',
            'url' => 'Failed to generate quiz from web content.',
            'document' => 'Failed to generate quiz from document.',
            'invalid_url' => 'Could not extract content from the provided URL.',
            'invalid_document' => 'Could not extract content from the uploaded document.',
            'ai_response_error' => 'Invalid response from AI service.',
        ],
        'placeholders' => [
            'content' => 'Paste your text content here (minimum 100 characters)...',
            'topic' => 'e.g., Photosynthesis in Plants',
            'subject' => 'e.g., Biology',
            'level' => 'e.g., Grade 10',
            'url' => 'https://example.com/article',
            'title' => 'Enter quiz title (optional)',
        ],
        'hints' => [
            'content_length' => 'Provide at least 100 characters of meaningful content for better quiz generation.',
            'question_count' => 'Recommended: 5-20 questions for optimal learning experience.',
            'difficulty' => 'Choose difficulty based on your target audience level.',
            'question_types' => 'Mix different question types for comprehensive assessment.',
        ],
    ],

    'sharing' => [
        'share_quiz' => 'Share Quiz',
        'public_sharing' => 'Public Sharing',
        'share_settings' => 'Share Settings',
        'generate_link' => 'Generate Share Link',
        'share_link' => 'Share Link',
        'public_access' => 'Public Access',
        'copy_link' => 'Copy Link',
        'link_copied' => 'Link copied to clipboard!',
        'link_generated' => 'Share link generated successfully.',
        'settings_updated' => 'Share settings updated successfully.',
        'revoked' => 'Sharing has been revoked.',
        'expired' => 'This share link has expired.',
        'invalid_code' => 'Invalid or expired share code.',
        'login_required' => 'Please login to access this quiz.',
        'team_access_required' => 'You must be a member of the team to access this quiz.',
        'deactivated' => 'This share link has been deactivated.',
        'manage_sharing' => 'Manage Sharing',
        'participant_management' => 'Participant Management',
        'sharing_analytics' => 'Sharing Analytics',
        'access_control' => 'Access Control',
        'require_registration' => 'Require Registration',
        'require_approval' => 'Require Approval',
        'attempt_limits' => 'Attempt Limits',
        'unlimited_attempts' => 'Unlimited Attempts',
        'visibility' => [
            'public' => 'Public',
            'team' => 'Team Only',
            'private' => 'Private',
        ],
    ],

    'participant' => [
        'participant' => 'Participant',
        'participants' => 'Participants',
        'props' => [
            'name' => 'Name',
            'email' => 'Email',
            'phone' => 'Phone',
            'organization' => 'Organization',
            'message' => 'Message',
            'registered_at' => 'Registered At',
            'status' => 'Status',
            'approval_required' => 'Approval Required',
        ],
        'status' => [
            'pending' => 'Pending',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
        ],
        'register' => 'Register for Quiz',
        'approve' => 'Approve',
        'reject' => 'Reject',
        'registration_required' => 'Registration required to take this quiz.',
        'awaiting_approval' => 'Your registration is awaiting approval.',
        'already_registered' => 'You are already registered for this quiz.',
        'registered' => 'Successfully registered for the quiz.',
        'approved' => 'Participant approved successfully.',
        'rejected' => 'Participant rejected successfully.',
        'deleted' => 'Participant removed successfully.',
        'not_found' => 'Participant not found.',
        'registration_not_required' => 'Registration is not required for this quiz.',
        'approval_required' => 'Your registration requires approval before you can take the quiz.',
        'name_required' => 'Name is required.',
        'name_string' => 'Name must be a string.',
        'name_max' => 'Name cannot exceed 255 characters.',
        'email_required' => 'Email is required.',
        'email_invalid' => 'Please provide a valid email address.',
        'email_max' => 'Email cannot exceed 255 characters.',
        'phone_string' => 'Phone must be a string.',
        'phone_max' => 'Phone cannot exceed 20 characters.',
        'organization_string' => 'Organization must be a string.',
        'organization_max' => 'Organization cannot exceed 255 characters.',
        'status_required' => 'Status is required.',
        'status_invalid' => 'Invalid status selected.',
        'message_string' => 'Message must be a string.',
        'message_max' => 'Message cannot exceed 500 characters.',
    ],



    'collection' => [
        'collection' => 'Collection',
        'collections' => 'Collections',
        'my_collections' => 'My Collections',
        'create_collection' => 'Create Collection',
        'add_to_collection' => 'Add to Collection',
        'remove_from_collection' => 'Remove from Collection',
        'props' => [
            'name' => 'Collection Name',
            'description' => 'Description',
            'is_public' => 'Public Collection',
            'quiz_count' => 'Quiz Count',
        ],
    ],

    'discovery' => [
        'discover_quizzes' => 'Discover Quizzes',
        'popular_quizzes' => 'Popular Quizzes',
        'recent_quizzes' => 'Recent Quizzes',
        'recommended_quizzes' => 'Recommended for You',
        'peer_quizzes' => 'Peer Created Quizzes',
        'filter_by_category' => 'Filter by Category',
        'filter_by_difficulty' => 'Filter by Difficulty',
        'search_quizzes' => 'Search Quizzes',
    ],

    'analytics' => [
        'analytics' => 'Analytics',
        'quiz_analytics' => 'Quiz Analytics',
        'performance_overview' => 'Performance Overview',
        'attempt_statistics' => 'Attempt Statistics',
        'question_analysis' => 'Question Analysis',
        'participant_insights' => 'Participant Insights',
        'time_analysis' => 'Time Analysis',
        'completion_rate' => 'Completion Rate',
        'average_score' => 'Average Score',
        'total_attempts' => 'Total Attempts',
        'unique_participants' => 'Unique Participants',
        'most_difficult_question' => 'Most Difficult Question',
        'easiest_question' => 'Easiest Question',
        'average_time_spent' => 'Average Time Spent',
        'export_analytics' => 'Export Analytics',
        'download_report' => 'Download Report',
        'time_range' => 'Time Range',
        'last_7_days' => 'Last 7 Days',
        'last_30_days' => 'Last 30 Days',
        'last_90_days' => 'Last 90 Days',
        'all_time' => 'All Time',
    ],

    'notifications' => [
        'notifications' => 'Notifications',
        'quiz_notifications' => 'Quiz Notifications',
        'participant_registered' => 'New participant registered for quiz',
        'participant_approved' => 'Participant approved for quiz',
        'participant_rejected' => 'Participant rejected for quiz',
        'quiz_shared' => 'Quiz shared publicly',
        'quiz_attempt_completed' => 'Quiz attempt completed',
        'mark_as_read' => 'Mark as Read',
        'mark_all_as_read' => 'Mark All as Read',
        'no_notifications' => 'No notifications found',
        'unread_count' => 'Unread Notifications',
    ],

    'actions' => [
        'create' => 'Create Quiz',
        'edit' => 'Edit Quiz',
        'delete' => 'Delete Quiz',
        'duplicate' => 'Duplicate Quiz',
        'publish' => 'Publish Quiz',
        'unpublish' => 'Unpublish Quiz',
        'preview' => 'Preview Quiz',
        'take_quiz' => 'Take Quiz',
        'view_results' => 'View Results',
        'view_analytics' => 'View Analytics',
        'manage_sharing' => 'Manage Sharing',
        'export' => 'Export Quiz',
        'import' => 'Import Quiz',
        'generate_ai' => 'Generate with AI',
    ],

    'messages' => [
        'created' => 'Quiz created successfully.',
        'updated' => 'Quiz updated successfully.',
        'deleted' => 'Quiz deleted successfully.',
        'published' => 'Quiz published successfully.',
        'unpublished' => 'Quiz unpublished successfully.',
        'duplicated' => 'Quiz duplicated successfully.',
        'imported' => 'Quiz imported successfully.',
        'exported' => 'Quiz exported successfully.',
        'attempt_started' => 'Quiz attempt started.',
        'started' => 'Quiz attempt started.',
        'attempt_submitted' => 'Quiz attempt submitted successfully.',
        'submitted' => 'Quiz attempt submitted successfully.',
        'progress_saved' => 'Progress saved successfully.',
        'not_completed' => 'Quiz attempt is not completed yet.',
        'no_questions' => 'This quiz has no questions yet.',
        'quiz_not_found' => 'Quiz not found.',
        'not_found' => 'Quiz not found.',
        'quiz_not_published' => 'This quiz is not published yet.',
        'not_published' => 'This quiz is not published.',
        'attempt_limit_reached' => 'You have reached the maximum number of attempts for this quiz.',
        'max_attempts_reached' => 'Maximum attempts reached for this quiz.',
        'time_expired' => 'Time limit for this quiz has expired.',
        'already_completed' => 'You have already completed this quiz.',
        'will_be_available_from' => 'This quiz will be available from :date.',
        'ended_on' => 'This quiz ended on :date.',
    ],

    'validation' => [
        'title_required' => 'Quiz title is required.',
        'category_required' => 'Quiz category is required.',
        'questions_required' => 'Quiz must have at least one question.',
        'question_text_required' => 'Question text is required.',
        'correct_answer_required' => 'Correct answer is required.',
        'options_required' => 'Multiple choice questions must have options.',
        'min_options' => 'Multiple choice questions must have at least 2 options.',
        'max_options' => 'Multiple choice questions can have at most 6 options.',
        'content_min_length' => 'Content must be at least 100 characters long.',
        'question_count_range' => 'Question count must be between 1 and 50.',
        'time_limit_positive' => 'Time limit must be a positive number.',
        'attempt_limit_range' => 'Attempt limit must be between 0 and 99.',
    ],

    'auto_save' => [
        'saved' => 'Auto-saved',
        'saving' => 'Saving...',
        'error' => 'Save failed',
    ],

    'quiz_average' => 'Quiz Average',
    'loading_results' => 'Loading results...',
    'loading_quiz' => 'Loading quiz...',
    'time_remaining' => 'Time Remaining',
    'auto_saved' => 'Auto-saved',
    'saving' => 'Saving...',
    'answered' => 'answered',
    'complete' => 'complete',
    'question_image' => 'Question Image',
    'true' => 'True',
    'false' => 'False',
    'submit_quiz' => 'Submit Quiz',
    'confirm_submit' => 'Are you sure you want to submit this quiz?',
    'cannot_change_answers' => 'You will not be able to change your answers after submission.',
    'out_of' => 'out of',
    'questions_correct' => 'questions correct',
    'passed' => 'Passed',
    'failed' => 'Failed',
    'quiz_information' => 'Quiz Information',
    'not_published' => 'This quiz is not published',
];
