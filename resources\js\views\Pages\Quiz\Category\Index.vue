<template>
    <ListItem
        :init-url="initUrl"
        :user-actions="userActions"
        :dropdown-actions="dropdownActions"
        @setItems="setItems"
    >
        <template #header>
            <PageHeader>
                <template #title>
                    <span>{{ $trans('quiz.category.categories') }}</span>
                </template>
            </PageHeader>
        </template>

        <template #filter>
            <ParentTransition appear :visibility="showFilter">
                <CategoryFilterForm
                    @refresh="emitter.emit('listItems')"
                    @hide="showFilter = false"
                ></CategoryFilterForm>
            </ParentTransition>
        </template>

        <ParentTransition appear :visibility="true">
            <DataTable
                :header="categories.headers"
                :meta="categories.meta"
                module="quiz-category"
                @refresh="emitter.emit('listItems')"
            >
                <DataRow
                    v-for="category in categories.data"
                    :key="category.uuid"
                    @double-click="editCategory(category)"
                >
                    <DataCell name="name">
                        <div class="flex items-center">
                            <div 
                                class="w-4 h-4 rounded mr-3"
                                :style="{ backgroundColor: category.color || '#6B7280' }"
                            ></div>
                            <div>
                                <div class="font-medium">{{ category.name }}</div>
                                <div class="text-sm text-gray-500" v-if="category.description">
                                    {{ category.description }}
                                </div>
                            </div>
                        </div>
                    </DataCell>

                    <DataCell name="quizCount">
                        <div class="flex items-center">
                            <i class="fas fa-question-circle text-blue-500 mr-2"></i>
                            <span class="font-medium">{{ category.quizCount || 0 }}</span>
                            <span class="text-sm text-gray-500 ml-1">{{ $trans('quiz.quizzes') }}</span>
                        </div>
                    </DataCell>

                    <DataCell name="isActive">
                        <span 
                            class="px-2 py-1 text-xs font-medium rounded-full"
                            :class="category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                        >
                            {{ category.isActive ? $trans('global.active') : $trans('global.inactive') }}
                        </span>
                    </DataCell>

                    <DataCell name="createdBy">
                        <div class="flex items-center">
                            <img 
                                v-if="category.creator?.avatar" 
                                :src="category.creator.avatar" 
                                :alt="category.creator.name"
                                class="w-6 h-6 rounded-full mr-2"
                            />
                            <div v-else class="w-6 h-6 bg-gray-300 rounded-full mr-2 flex items-center justify-center">
                                <i class="fas fa-user text-gray-600 text-xs"></i>
                            </div>
                            <span class="text-sm">{{ category.creator?.name }}</span>
                        </div>
                    </DataCell>

                    <DataCell name="createdAt">
                        <span class="text-sm text-gray-600">
                            {{ $cal.toUserTimezone(category.createdAt, 'date') }}
                        </span>
                    </DataCell>

                    <DataCell name="actions">
                        <div class="flex items-center space-x-2">
                            <button
                                @click="viewCategory(category)"
                                class="text-blue-600 hover:text-blue-800 text-sm"
                                :title="$trans('global.view')"
                            >
                                <i class="fas fa-eye"></i>
                            </button>

                            <button
                                @click="editCategory(category)"
                                class="text-green-600 hover:text-green-800 text-sm"
                                :title="$trans('global.edit')"
                            >
                                <i class="fas fa-edit"></i>
                            </button>

                            <button
                                @click="toggleStatus(category)"
                                class="text-yellow-600 hover:text-yellow-800 text-sm"
                                :title="category.isActive ? $trans('global.deactivate') : $trans('global.activate')"
                            >
                                <i :class="category.isActive ? 'fas fa-toggle-on' : 'fas fa-toggle-off'"></i>
                            </button>

                            <button
                                v-if="canDelete(category)"
                                @click="deleteCategory(category)"
                                class="text-red-600 hover:text-red-800 text-sm"
                                :title="$trans('global.delete')"
                            >
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </DataCell>
                </DataRow>
            </DataTable>
        </ParentTransition>
    </ListItem>
</template>

<script>
export default {
    name: "QuizCategoryList",
}
</script>

<script setup>
import { ref, reactive, inject } from "vue"
import { useRouter } from "vue-router"
import { useEmitter } from "@core/composables"
import CategoryFilterForm from "./FilterForm.vue"

const router = useRouter()
const emitter = useEmitter()
const $cal = inject("$cal")

const initUrl = "quiz/category/"
const showFilter = ref(false)
const categories = reactive({})

const userActions = [
    {
        name: "add",
        icon: "fas fa-plus",
        label: "quiz.category.add_category",
        action: () => router.push({ name: "QuizCategoryCreate" })
    }
]

const dropdownActions = ["filter"]

const setItems = (data) => {
    Object.assign(categories, data)
}

// Removed custom formatDate - using $cal.toUserTimezone instead

const canDelete = (category) => {
    // Don't allow deletion if category has quizzes
    return (category.quizCount || 0) === 0
}

const viewCategory = (category) => {
    router.push({
        name: 'QuizCategoryShow',
        params: { uuid: category.uuid }
    })
}

const editCategory = (category) => {
    router.push({
        name: 'QuizCategoryEdit',
        params: { uuid: category.uuid }
    })
}

const toggleStatus = async (category) => {
    try {
        // TODO: Implement API call to toggle category status
        console.log('Toggle category status:', category)
        category.isActive = !category.isActive
        emitter.emit('listItems')
    } catch (error) {
        console.error('Error toggling category status:', error)
    }
}

const deleteCategory = async (category) => {
    if (!confirm('Are you sure you want to delete this category?')) {
        return
    }

    try {
        // TODO: Implement API call to delete category
        console.log('Delete category:', category)
        emitter.emit('listItems')
    } catch (error) {
        console.error('Error deleting category:', error)
    }
}
</script>
