<?php

namespace App\Http\Controllers\Quiz;

use App\Http\Controllers\Controller;
use App\Http\Requests\Quiz\QuizCategoryRequest;
use App\Http\Resources\Quiz\QuizCategoryResource;
use App\Models\Quiz\QuizCategory;
use App\Services\Quiz\QuizCategoryListService;
use App\Services\Quiz\QuizCategoryService;
use Illuminate\Http\Request;

class QuizCategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('test.mode.restriction')->only(['destroy']);
    }

    public function preRequisite(Request $request, QuizCategoryService $service)
    {
        return response()->ok($service->preRequisite($request));
    }

    public function index(Request $request, QuizCategoryListService $service)
    {
        $this->authorize('viewAny', QuizCategory::class);

        return $service->paginate($request);
    }

    public function store(QuizCategoryRequest $request, QuizCategoryService $service)
    {
        $this->authorize('create', QuizCategory::class);

        $category = $service->create($request);

        return response()->success([
            'message' => trans('global.created', ['attribute' => trans('quiz.category.category')]),
            'category' => QuizCategoryResource::make($category),
        ]);
    }

    public function show(string $category, QuizCategoryService $service)
    {
        $category = $service->findByUuidOrFail($category);

        $this->authorize('view', $category);

        $category->load('children', 'parent');

        return QuizCategoryResource::make($category);
    }

    public function update(QuizCategoryRequest $request, string $category, QuizCategoryService $service)
    {
        $category = $service->findByUuidOrFail($category);

        $this->authorize('update', $category);

        $service->update($request, $category);

        return response()->success([
            'message' => trans('global.updated', ['attribute' => trans('quiz.category.category')]),
        ]);
    }

    public function destroy(string $category, QuizCategoryService $service)
    {
        $category = $service->findByUuidOrFail($category);

        $this->authorize('delete', $category);

        $service->deletable($category);

        $service->delete($category);

        return response()->success([
            'message' => trans('global.deleted', ['attribute' => trans('quiz.category.category')]),
        ]);
    }
}
