<template>
    <div class="space-y-6">
        <!-- Header -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $trans('quiz.sharing.manage_sharing') }}</h1>
                <p class="text-gray-600 mt-1">{{ quiz.title }}</p>
            </div>
            <div class="flex space-x-3">
                <button
                    @click="refreshData"
                    class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                    <i class="fas fa-sync-alt mr-2"></i>
                    {{ $trans('global.refresh') }}
                </button>
                <router-link
                    :to="{ name: 'QuizShow', params: { uuid: route.params.uuid } }"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
                >
                    {{ $trans('quiz.back_to_quiz') }}
                </router-link>
            </div>
        </div>

        <!-- Sharing Settings -->
        <BaseCard>
            <template #title>
                {{ $trans('quiz.sharing.settings') }}
            </template>
            
            <div class="space-y-6">
                <!-- Share Link -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        {{ $trans('quiz.sharing.share_link') }}
                    </label>
                    <div class="flex space-x-2">
                        <input
                            :value="shareUrl"
                            readonly
                            class="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                        />
                        <button
                            @click="copyShareLink"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                        >
                            <i class="fas fa-copy mr-2"></i>
                            {{ $trans('global.copy') }}
                        </button>
                        <button
                            v-if="quiz.isShared"
                            @click="revokeSharing"
                            class="px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700"
                        >
                            {{ $trans('quiz.sharing.revoke') }}
                        </button>
                        <button
                            v-else
                            @click="generateShareLink"
                            class="px-4 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
                        >
                            {{ $trans('quiz.sharing.generate') }}
                        </button>
                    </div>
                </div>

                <!-- Sharing Options -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            {{ $trans('quiz.sharing.require_registration') }}
                        </label>
                        <BaseSwitch
                            v-model="settings.requireRegistration"
                            @change="updateSettings"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            {{ $trans('quiz.sharing.require_approval') }}
                        </label>
                        <BaseSwitch
                            v-model="settings.requireApproval"
                            @change="updateSettings"
                            :disabled="!settings.requireRegistration"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            {{ $trans('quiz.sharing.show_correct_answers') }}
                        </label>
                        <BaseSwitch
                            v-model="settings.showCorrectAnswers"
                            @change="updateSettings"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            {{ $trans('quiz.sharing.max_attempts') }}
                        </label>
                        <BaseInput
                            v-model="settings.maxAttempts"
                            type="number"
                            min="0"
                            max="99"
                            @blur="updateSettings"
                            placeholder="0 = unlimited"
                        />
                    </div>
                </div>

                <!-- Date Range -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            {{ $trans('quiz.sharing.start_date') }}
                        </label>
                        <BaseInput
                            v-model="settings.startDate"
                            type="datetime-local"
                            @change="updateSettings"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            {{ $trans('quiz.sharing.end_date') }}
                        </label>
                        <BaseInput
                            v-model="settings.endDate"
                            type="datetime-local"
                            @change="updateSettings"
                        />
                    </div>
                </div>
            </div>
        </BaseCard>

        <!-- Participants Management -->
        <BaseCard v-if="settings.requireRegistration">
            <template #title>
                <div class="flex justify-between items-center">
                    <span>{{ $trans('quiz.participant.participants') }}</span>
                    <div class="flex space-x-2 text-sm">
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">
                            {{ pendingCount }} {{ $trans('quiz.participant.status.pending') }}
                        </span>
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded">
                            {{ approvedCount }} {{ $trans('quiz.participant.status.approved') }}
                        </span>
                        <span class="px-2 py-1 bg-red-100 text-red-800 rounded">
                            {{ rejectedCount }} {{ $trans('quiz.participant.status.rejected') }}
                        </span>
                    </div>
                </div>
            </template>

            <!-- Filters -->
            <div class="mb-4 flex space-x-4">
                <BaseSelect
                    v-model="participantFilter"
                    @change="fetchParticipants"
                    class="w-48"
                >
                    <option value="all">{{ $trans('quiz.participant.all') }}</option>
                    <option value="pending">{{ $trans('quiz.participant.status.pending') }}</option>
                    <option value="approved">{{ $trans('quiz.participant.status.approved') }}</option>
                    <option value="rejected">{{ $trans('quiz.participant.status.rejected') }}</option>
                </BaseSelect>
                
                <BaseInput
                    v-model="searchQuery"
                    @input="debouncedSearch"
                    placeholder="Search participants..."
                    class="flex-1"
                >
                    <template #prefix>
                        <i class="fas fa-search text-gray-400"></i>
                    </template>
                </BaseInput>
            </div>

            <!-- Participants Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ $trans('quiz.participant.name') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ $trans('quiz.participant.email') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ $trans('quiz.participant.organization') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ $trans('quiz.participant.status.label') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ $trans('quiz.participant.registered_at') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ $trans('global.actions') }}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="participant in participants" :key="participant.id">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ participant.name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ participant.email }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ participant.organization || '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                    :class="getStatusClass(participant.status)"
                                >
                                    {{ $trans(`quiz.participant.status.${participant.status}`) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $cal.toUserTimezone(participant.createdAt, 'date') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button
                                    v-if="participant.status === 'pending'"
                                    @click="approveParticipant(participant)"
                                    class="text-green-600 hover:text-green-900"
                                >
                                    {{ $trans('quiz.approve') }}
                                </button>
                                <button
                                    v-if="participant.status === 'pending'"
                                    @click="rejectParticipant(participant)"
                                    class="text-red-600 hover:text-red-900"
                                >
                                    {{ $trans('quiz.reject') }}
                                </button>
                                <button
                                    @click="viewParticipantAttempts(participant)"
                                    class="text-blue-600 hover:text-blue-900"
                                >
                                    {{ $trans('quiz.view_attempts') }}
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Empty State -->
            <div v-if="participants.length === 0" class="text-center py-8">
                <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-600">{{ $trans('quiz.participant.no_participants') }}</p>
            </div>
        </BaseCard>

        <!-- Analytics Summary -->
        <BaseCard>
            <template #title>
                {{ $trans('quiz.sharing.analytics_summary') }}
            </template>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600">{{ analytics.totalViews || 0 }}</div>
                    <div class="text-sm text-gray-600">{{ $trans('quiz.sharing.total_views') }}</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600">{{ analytics.totalAttempts || 0 }}</div>
                    <div class="text-sm text-gray-600">{{ $trans('quiz.total_attempts') }}</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600">{{ analytics.conversionRate || 0 }}%</div>
                    <div class="text-sm text-gray-600">{{ $trans('quiz.sharing.conversion_rate') }}</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-orange-600">{{ analytics.averageScore || 0 }}%</div>
                    <div class="text-sm text-gray-600">{{ $trans('quiz.average_score') }}</div>
                </div>
            </div>
        </BaseCard>
    </div>
</template>

<script>
export default {
    name: "QuizSharingIndex",
}
</script>

<script setup>
import { reactive, ref, computed, onMounted, inject } from "vue"
import { useRoute, useRouter } from "vue-router"
import { useStore } from "vuex"
import { useToast } from "vue-toastification"
import { debounce } from "lodash"

const route = useRoute()
const router = useRouter()
const store = useStore()
const toast = useToast()
const $cal = inject("$cal")

const quiz = reactive({})
const settings = reactive({
    requireRegistration: false,
    requireApproval: false,
    showCorrectAnswers: true,
    maxAttempts: 0,
    startDate: null,
    endDate: null,
})
const participants = ref([])
const analytics = reactive({})
const participantFilter = ref('all')
const searchQuery = ref('')

const shareUrl = computed(() => {
    if (quiz.shareCode) {
        return `${window.location.origin}/quiz/public/${quiz.shareCode}`
    }
    return ''
})

const pendingCount = computed(() => participants.value.filter(p => p.status === 'pending').length)
const approvedCount = computed(() => participants.value.filter(p => p.status === 'approved').length)
const rejectedCount = computed(() => participants.value.filter(p => p.status === 'rejected').length)

const fetchData = async () => {
    try {
        // Fetch quiz details
        const quizResponse = await store.dispatch('quiz/show', route.params.uuid)
        Object.assign(quiz, quizResponse.quiz)
        Object.assign(settings, {
            requireRegistration: quiz.requireRegistration,
            requireApproval: quiz.requireApproval,
            showCorrectAnswers: quiz.showCorrectAnswers,
            maxAttempts: quiz.maxAttempts,
            startDate: quiz.startDate,
            endDate: quiz.endDate,
        })

        // Fetch participants if registration is required
        if (settings.requireRegistration) {
            await fetchParticipants()
        }

        // Fetch analytics
        await fetchAnalytics()
    } catch (error) {
        console.error('Error fetching data:', error)
    }
}

const fetchParticipants = async () => {
    try {
        const response = await store.dispatch('quiz/notification/getParticipants', route.params.uuid)
        participants.value = response.participants || []
    } catch (error) {
        console.error('Error fetching participants:', error)
    }
}

const fetchAnalytics = async () => {
    try {
        const response = await store.dispatch('quiz/getAnalytics', route.params.uuid)
        Object.assign(analytics, response.analytics)
    } catch (error) {
        console.error('Error fetching analytics:', error)
    }
}

const generateShareLink = async () => {
    try {
        await store.dispatch('quiz/generateShareLink', route.params.uuid)
        await fetchData() // Refresh data
    } catch (error) {
        console.error('Error generating share link:', error)
    }
}

const revokeSharing = async () => {
    if (confirm('Are you sure you want to revoke sharing? This will disable the public link.')) {
        try {
            await store.dispatch('quiz/revokeSharing', route.params.uuid)
            await fetchData() // Refresh data
        } catch (error) {
            console.error('Error revoking sharing:', error)
        }
    }
}

const copyShareLink = async () => {
    try {
        await navigator.clipboard.writeText(shareUrl.value)
        toast.success('Share link copied to clipboard')
    } catch (error) {
        console.error('Error copying to clipboard:', error)
        toast.error('Failed to copy link')
    }
}

const updateSettings = async () => {
    try {
        await store.dispatch('quiz/update', {
            uuid: route.params.uuid,
            ...settings
        })
        toast.success('Settings updated successfully')
    } catch (error) {
        console.error('Error updating settings:', error)
    }
}

const approveParticipant = async (participant) => {
    try {
        await store.dispatch('quiz/notification/approveParticipant', {
            quizUuid: route.params.uuid,
            participantId: participant.id
        })
        await fetchParticipants()
    } catch (error) {
        console.error('Error approving participant:', error)
    }
}

const rejectParticipant = async (participant) => {
    const reason = prompt('Reason for rejection (optional):')
    try {
        await store.dispatch('quiz/notification/rejectParticipant', {
            quizUuid: route.params.uuid,
            participantId: participant.id,
            reason: reason
        })
        await fetchParticipants()
    } catch (error) {
        console.error('Error rejecting participant:', error)
    }
}

const viewParticipantAttempts = (participant) => {
    router.push({
        name: 'QuizResults',
        params: { uuid: route.params.uuid },
        query: { participant: participant.email }
    })
}

const getStatusClass = (status) => {
    switch (status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800'
        case 'approved':
            return 'bg-green-100 text-green-800'
        case 'rejected':
            return 'bg-red-100 text-red-800'
        default:
            return 'bg-gray-100 text-gray-800'
    }
}

// Removed custom formatDate - using $cal.toUserTimezone instead

const refreshData = () => {
    fetchData()
}

const debouncedSearch = debounce(() => {
    fetchParticipants()
}, 300)

onMounted(() => {
    fetchData()
})
</script>
