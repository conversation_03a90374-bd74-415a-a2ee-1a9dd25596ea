<?php

namespace App\Http\Requests\Quiz;

use App\Http\Requests\BaseRequest;
use App\Models\Quiz\StudentQuizCollection;
use Illuminate\Validation\Rule;

class StudentQuizCollectionRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $collection = $this->route('studentQuizCollection');
        $isUpdate = $collection instanceof StudentQuizCollection || is_string($collection);
        $studentId = auth()->user()->student?->id;

        return [
            'name' => [
                'required',
                'string',
                'max:100',
                Rule::unique('student_quiz_collections', 'name')
                    ->where('student_id', $studentId)
                    ->ignore($isUpdate && $collection instanceof StudentQuizCollection ? $collection->id : null),
            ],
            'description' => 'nullable|string|max:1000',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_default' => 'boolean',
            'meta' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => trans('validation.required', ['attribute' => trans('quiz.collection.props.name')]),
            'name.unique' => trans('validation.unique', ['attribute' => trans('quiz.collection.props.name')]),
            'name.max' => trans('validation.max.string', ['attribute' => trans('quiz.collection.props.name'), 'max' => 100]),
            'description.max' => trans('validation.max.string', ['attribute' => trans('quiz.collection.props.description'), 'max' => 1000]),
            'color.regex' => trans('validation.regex', ['attribute' => trans('quiz.collection.props.color')]),
            'color.max' => trans('validation.max.string', ['attribute' => trans('quiz.collection.props.color'), 'max' => 7]),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => trans('quiz.collection.props.name'),
            'description' => trans('quiz.collection.props.description'),
            'color' => trans('quiz.collection.props.color'),
            'is_default' => trans('quiz.collection.props.is_default'),
        ];
    }
}
