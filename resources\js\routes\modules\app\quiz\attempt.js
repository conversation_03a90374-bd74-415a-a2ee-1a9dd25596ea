export default [
    {
        path: "attempts",
        name: "QuizAttempt",
        redirect: { name: "QuizAttemptList" },
        meta: {
            label: "quiz.attempt.attempts",
            icon: "fas fa-play",
            hideChildren: true,
            keySearch: true,
            permissions: ["quiz:attempt"],
        },
        component: {
            template: "<router-view></router-view>",
        },
        children: [
            {
                path: "",
                name: "QuizAttemptList",
                meta: {
                    trans: "global.list",
                    label: "quiz.attempt.attempts",
                    keySearch: true,
                },
                component: () => import("@views/Pages/Quiz/Attempt/Index.vue"),
            },
            {
                path: ":uuid/take",
                name: "QuizTake",
                meta: {
                    trans: "quiz.attempt.start_quiz",
                    label: "quiz.quiz",
                    isNotNav: true,
                },
                component: () => import("@views/Pages/Quiz/Attempt/Take.vue"),
            },
            {
                path: ":uuid/result",
                name: "QuizAttemptResult",
                meta: {
                    trans: "quiz.attempt.view_result",
                    label: "quiz.attempt.attempt",
                    isNotNav: true,
                },
                component: () => import("@views/Pages/Quiz/Attempt/Result.vue"),
            },
        ],
    },
]
