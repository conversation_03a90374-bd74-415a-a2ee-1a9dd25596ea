<template>
    <FilterForm
        :init-form="initForm"
        :form="form"
        :multiple="[]"
        @hide="emit('hide')"
    >
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Quiz Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.quiz') }}
                </label>
                <select
                    v-model="form.quizId"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option 
                        v-for="quiz in availableQuizzes" 
                        :key="quiz.uuid" 
                        :value="quiz.uuid"
                    >
                        {{ quiz.title }}
                    </option>
                </select>
            </div>

            <!-- Status Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.attempt.status') }}
                </label>
                <select
                    v-model="form.status"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="completed">{{ $trans('quiz.attempt.status.completed') }}</option>
                    <option value="in_progress">{{ $trans('quiz.attempt.status.in_progress') }}</option>
                    <option value="abandoned">{{ $trans('quiz.attempt.status.abandoned') }}</option>
                    <option value="expired">{{ $trans('quiz.attempt.status.expired') }}</option>
                </select>
            </div>

            <!-- Score Range Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.attempt.score_range') }}
                </label>
                <select
                    v-model="form.scoreRange"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="0-20">0-20%</option>
                    <option value="21-40">21-40%</option>
                    <option value="41-60">41-60%</option>
                    <option value="61-80">61-80%</option>
                    <option value="81-100">81-100%</option>
                </select>
            </div>

            <!-- Pass/Fail Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.attempt.result') }}
                </label>
                <select
                    v-model="form.passed"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="1">{{ $trans('quiz.passed') }}</option>
                    <option value="0">{{ $trans('quiz.failed') }}</option>
                </select>
            </div>

            <!-- Date Range Filters -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.attempt.started_from') }}
                </label>
                <input
                    v-model="form.startedFrom"
                    type="date"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.attempt.started_to') }}
                </label>
                <input
                    v-model="form.startedTo"
                    type="date"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
            </div>

            <!-- Participant Filter -->
            <div class="md:col-span-2 lg:col-span-3">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.attempt.participant') }}
                </label>
                <input
                    v-model="form.participant"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="$trans('quiz.attempt.search_by_participant')"
                />
            </div>

            <!-- Time Spent Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.attempt.time_spent_range') }}
                </label>
                <select
                    v-model="form.timeSpentRange"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="0-300">0-5 {{ $trans('quiz.minutes') }}</option>
                    <option value="301-600">5-10 {{ $trans('quiz.minutes') }}</option>
                    <option value="601-1200">10-20 {{ $trans('quiz.minutes') }}</option>
                    <option value="1201-1800">20-30 {{ $trans('quiz.minutes') }}</option>
                    <option value="1801+">30+ {{ $trans('quiz.minutes') }}</option>
                </select>
            </div>

            <!-- Category Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.category.category') }}
                </label>
                <select
                    v-model="form.categoryId"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option 
                        v-for="category in availableCategories" 
                        :key="category.uuid" 
                        :value="category.uuid"
                    >
                        {{ category.name }}
                    </option>
                </select>
            </div>

            <!-- Search Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('global.search') }}
                </label>
                <input
                    v-model="form.search"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="$trans('quiz.attempt.search_attempts')"
                />
            </div>
        </div>
    </FilterForm>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue"
import { useRoute } from "vue-router"

const route = useRoute()

const emit = defineEmits(["hide"])

const availableQuizzes = ref([])
const availableCategories = ref([])

const initForm = {
    quizId: "",
    status: "",
    scoreRange: "",
    passed: "",
    startedFrom: "",
    startedTo: "",
    participant: "",
    timeSpentRange: "",
    categoryId: "",
    search: "",
}

const form = reactive({ ...initForm })

const fetchData = reactive({
    isLoaded: true,
})

onMounted(async () => {
    fetchData.isLoaded = true
})
</script>
