<template>
    <ListItem
        :init-url="initUrl"
        :user-actions="userActions"
        :dropdown-actions="dropdownActions"
        @setItems="setItems"
    >
        <template #header>
            <PageHeader>
                <template #title>
                    <span>{{ $trans('quiz.attempt.attempts') }}</span>
                </template>
            </PageHeader>
        </template>

        <template #filter>
            <ParentTransition appear :visibility="showFilter">
                <AttemptFilterForm
                    @refresh="emitter.emit('listItems')"
                    @hide="showFilter = false"
                ></AttemptFilterForm>
            </ParentTransition>
        </template>

        <ParentTransition appear :visibility="true">
            <DataTable
                :header="attempts.headers"
                :meta="attempts.meta"
                module="quiz-attempt"
                @refresh="emitter.emit('listItems')"
            >
                <DataRow
                    v-for="attempt in attempts.data"
                    :key="attempt.uuid"
                    @double-click="viewResult(attempt)"
                >
                    <DataCell name="quiz">
                        <div class="flex flex-col">
                            <span class="font-medium">{{ attempt.quiz?.title }}</span>
                            <span class="text-sm text-gray-500" v-if="attempt.quiz?.description">
                                {{ attempt.quiz.description }}
                            </span>
                        </div>
                    </DataCell>

                    <DataCell name="participant">
                        <div class="flex items-center">
                            <img 
                                v-if="attempt.participant?.avatar" 
                                :src="attempt.participant.avatar" 
                                :alt="attempt.participant.name"
                                class="w-8 h-8 rounded-full mr-3"
                            />
                            <div v-else class="w-8 h-8 bg-gray-300 rounded-full mr-3 flex items-center justify-center">
                                <i class="fas fa-user text-gray-600"></i>
                            </div>
                            <div>
                                <div class="font-medium">{{ attempt.participant?.name }}</div>
                                <div class="text-sm text-gray-500">{{ attempt.participant?.email }}</div>
                            </div>
                        </div>
                    </DataCell>

                    <DataCell name="score">
                        <div class="flex items-center">
                            <span 
                                class="text-lg font-semibold mr-2"
                                :class="getScoreColor(attempt.score)"
                            >
                                {{ attempt.score }}%
                            </span>
                            <span 
                                class="px-2 py-1 text-xs font-medium rounded-full"
                                :class="attempt.passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                            >
                                {{ attempt.passed ? $trans('quiz.passed') : $trans('quiz.failed') }}
                            </span>
                        </div>
                    </DataCell>

                    <DataCell name="timeSpent">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-gray-400 mr-2"></i>
                            <span>{{ formatTime(attempt.timeSpent) }}</span>
                        </div>
                    </DataCell>

                    <DataCell name="status">
                        <span 
                            class="px-2 py-1 text-xs font-medium rounded-full"
                            :class="getStatusClass(attempt.status)"
                        >
                            {{ $trans(`quiz.attempt.status.${attempt.status}`) }}
                        </span>
                    </DataCell>

                    <DataCell name="startedAt">
                        <span class="text-sm text-gray-600">
                            {{ $cal.toUserTimezone(attempt.startedAt, 'datetime') }}
                        </span>
                    </DataCell>

                    <DataCell name="completedAt">
                        <span class="text-sm text-gray-600">
                            {{ attempt.completedAt ? $cal.toUserTimezone(attempt.completedAt, 'datetime') : '-' }}
                        </span>
                    </DataCell>

                    <DataCell name="actions">
                        <div class="flex items-center space-x-2">
                            <button
                                v-if="attempt.status === 'completed'"
                                @click="viewResult(attempt)"
                                class="text-blue-600 hover:text-blue-800 text-sm"
                                :title="$trans('quiz.attempt.view_result')"
                            >
                                <i class="fas fa-chart-bar"></i>
                            </button>

                            <button
                                v-if="attempt.status === 'in_progress'"
                                @click="continueAttempt(attempt)"
                                class="text-green-600 hover:text-green-800 text-sm"
                                :title="$trans('quiz.attempt.continue')"
                            >
                                <i class="fas fa-play"></i>
                            </button>

                            <button
                                v-if="canRetake(attempt)"
                                @click="retakeQuiz(attempt)"
                                class="text-purple-600 hover:text-purple-800 text-sm"
                                :title="$trans('quiz.attempt.retake')"
                            >
                                <i class="fas fa-redo"></i>
                            </button>

                            <button
                                @click="exportResult(attempt)"
                                class="text-gray-600 hover:text-gray-800 text-sm"
                                :title="$trans('quiz.attempt.export')"
                            >
                                <i class="fas fa-download"></i>
                            </button>

                            <button
                                v-if="canDelete(attempt)"
                                @click="deleteAttempt(attempt)"
                                class="text-red-600 hover:text-red-800 text-sm"
                                :title="$trans('global.delete')"
                            >
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </DataCell>
                </DataRow>
            </DataTable>
        </ParentTransition>
    </ListItem>
</template>

<script>
export default {
    name: "QuizAttemptList",
}
</script>

<script setup>
import { ref, reactive, inject } from "vue"
import { useRouter } from "vue-router"
import { useEmitter } from "@core/composables"
import AttemptFilterForm from "./FilterForm.vue"

const router = useRouter()
const emitter = useEmitter()
const $cal = inject("$cal")

const initUrl = "quiz/attempt/"
const showFilter = ref(false)
const attempts = reactive({})

const userActions = []
const dropdownActions = ["filter"]

const setItems = (data) => {
    Object.assign(attempts, data)
}

const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
}

const getStatusClass = (status) => {
    switch (status) {
        case 'completed':
            return 'bg-green-100 text-green-800'
        case 'in_progress':
            return 'bg-blue-100 text-blue-800'
        case 'abandoned':
            return 'bg-red-100 text-red-800'
        case 'expired':
            return 'bg-gray-100 text-gray-800'
        default:
            return 'bg-gray-100 text-gray-800'
    }
}

const formatTime = (seconds) => {
    if (!seconds) return '0:00'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// Removed custom formatDate - using $cal.toUserTimezone instead

const canRetake = (attempt) => {
    return attempt.quiz?.allowRetakes && attempt.status === 'completed'
}

const canDelete = (attempt) => {
    // TODO: Implement permission check
    return true
}

const viewResult = (attempt) => {
    router.push({
        name: 'QuizAttemptResult',
        params: { uuid: attempt.uuid }
    })
}

const continueAttempt = (attempt) => {
    router.push({
        name: 'QuizTake',
        params: { uuid: attempt.quiz.uuid },
        query: { attemptId: attempt.uuid }
    })
}

const retakeQuiz = (attempt) => {
    router.push({
        name: 'QuizTake',
        params: { uuid: attempt.quiz.uuid }
    })
}

const exportResult = (attempt) => {
    // TODO: Implement export functionality
    console.log('Export result:', attempt)
}

const deleteAttempt = (attempt) => {
    // TODO: Implement delete functionality
    console.log('Delete attempt:', attempt)
}
</script>
