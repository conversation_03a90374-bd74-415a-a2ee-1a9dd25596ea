<?php

namespace App\Http\Resources\Quiz;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuizCollectionItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'uuid' => $this->uuid,
            'position' => $this->position,
            'addedAt' => $this->added_at,
            'quiz' => $this->whenLoaded('quiz', function () {
                return QuizResource::make($this->quiz);
            }),
            'collection' => $this->whenLoaded('collection', function () {
                return StudentQuizCollectionResource::make($this->collection);
            }),
            'meta' => $this->meta,
            'createdAt' => $this->created_at,
            'updatedAt' => $this->updated_at,
        ];
    }
}
