<?php

namespace App\Http\Controllers\Quiz;

use App\Http\Controllers\Controller;
use App\Http\Requests\Quiz\StudentQuizCollectionRequest;
use App\Http\Resources\Quiz\StudentQuizCollectionResource;
use App\Models\Quiz\Quiz;
use App\Models\Quiz\QuizCollectionItem;
use App\Services\Quiz\StudentQuizService;
use Illuminate\Http\Request;

class StudentQuizController extends Controller
{
    public function preRequisite(Request $request, StudentQuizService $service)
    {
        return response()->ok($service->preRequisite($request));
    }

    public function index(Request $request, StudentQuizService $service)
    {
        $student = auth()->user()->student;
        
        if (!$student) {
            return response()->error(['message' => trans('quiz.student.not_found')]);
        }

        $collections = $service->getStudentCollections($student);

        return response()->ok([
            'collections' => StudentQuizCollectionResource::collection($collections),
        ]);
    }

    public function store(StudentQuizCollectionRequest $request, StudentQuizService $service)
    {
        $student = auth()->user()->student;
        
        if (!$student) {
            return response()->error(['message' => trans('quiz.student.not_found')]);
        }

        $collection = $service->createCollection($request, $student);

        return response()->success([
            'message' => trans('global.created', ['attribute' => trans('quiz.collection.collection')]),
            'collection' => StudentQuizCollectionResource::make($collection),
        ]);
    }

    public function show(string $studentQuizCollection, StudentQuizService $service)
    {
        $collection = $service->findCollectionByUuid($studentQuizCollection);

        $this->authorize('view', $collection);

        $collection->load(['items.quiz.category', 'items.quiz.user', 'student']);

        return StudentQuizCollectionResource::make($collection);
    }

    public function update(StudentQuizCollectionRequest $request, string $studentQuizCollection, StudentQuizService $service)
    {
        $collection = $service->findCollectionByUuid($studentQuizCollection);

        $this->authorize('update', $collection);

        $service->updateCollection($request, $collection);

        return response()->success([
            'message' => trans('global.updated', ['attribute' => trans('quiz.collection.collection')]),
        ]);
    }

    public function destroy(string $studentQuizCollection, StudentQuizService $service)
    {
        $collection = $service->findCollectionByUuid($studentQuizCollection);

        $this->authorize('delete', $collection);

        $service->deleteCollection($collection);

        return response()->success([
            'message' => trans('global.deleted', ['attribute' => trans('quiz.collection.collection')]),
        ]);
    }

    public function addItem(Request $request, string $studentQuizCollection, StudentQuizService $service)
    {
        $collection = $service->findCollectionByUuid($studentQuizCollection);
        $quiz = Quiz::query()->where('uuid', $request->quiz_uuid)->firstOrFail();

        $this->authorize('update', $collection);

        $service->addQuizToCollection($request, $collection, $quiz);

        return response()->success([
            'message' => trans('quiz.collection.quiz_added'),
        ]);
    }

    public function removeItem(string $studentQuizCollection, string $quizCollectionItem, StudentQuizService $service)
    {
        $collection = $service->findCollectionByUuid($studentQuizCollection);
        $item = QuizCollectionItem::query()->where('uuid', $quizCollectionItem)->firstOrFail();

        $this->authorize('update', $collection);

        $service->removeQuizFromCollection($collection, $item->quiz);

        return response()->success([
            'message' => trans('quiz.collection.quiz_removed'),
        ]);
    }

    public function discover(Request $request, StudentQuizService $service)
    {
        $quizzes = $service->getDiscoverableQuizzes($request);

        return response()->ok([
            'quizzes' => $quizzes,
        ]);
    }

    public function addToCollection(Request $request, string $quiz, StudentQuizService $service)
    {
        $quiz = Quiz::query()->where('uuid', $quiz)->firstOrFail();
        $collection = $service->findCollectionByUuid($request->collection_uuid);

        $this->authorize('update', $collection);

        $service->addQuizToCollection($request, $collection, $quiz);

        return response()->success([
            'message' => trans('quiz.collection.quiz_added'),
        ]);
    }
}
