export default [
    {
        path: "categories",
        name: "QuizCategory",
        redirect: { name: "QuizCategoryList" },
        meta: {
            label: "quiz.category.categories",
            icon: "fas fa-tags",
            hideChildren: true,
            keySearch: true,
            permissions: ["quiz-category:read"],
        },
        component: {
            template: "<router-view></router-view>",
        },
        children: [
            {
                path: "",
                name: "QuizCategoryList",
                meta: {
                    trans: "global.list",
                    label: "quiz.category.categories",
                    keySearch: true,
                },
                component: () => import("@views/Pages/Quiz/Category/Index.vue"),
            },
            {
                path: "create",
                name: "QuizCategoryCreate",
                meta: {
                    type: "create",
                    action: "create",
                    trans: "global.add",
                    label: "quiz.category.category",
                    permissions: ["quiz-category:create"],
                },
                component: () => import("@views/Pages/Quiz/Category/Action.vue"),
            },
            {
                path: ":uuid/edit",
                name: "QuizCategoryEdit",
                meta: {
                    type: "edit",
                    action: "update",
                    trans: "global.edit",
                    label: "quiz.category.category",
                    permissions: ["quiz-category:edit"],
                },
                component: () => import("@views/Pages/Quiz/Category/Action.vue"),
            },
        ],
    },
]
