<?php

namespace App\Services\Quiz;

use App\Http\Resources\Quiz\QuizCategoryResource;
use App\Models\Quiz\QuizCategory;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class QuizCategoryService
{
    public function preRequisite(Request $request): array
    {
        $categories = QuizCategoryResource::collection(QuizCategory::query()
            ->byTeam()
            ->rootCategories()
            ->with('children')
            ->get());

        return compact('categories');
    }

    public function findByUuidOrFail(string $uuid): QuizCategory
    {
        return QuizCategory::query()
            ->byTeam()
            ->findByUuidOrFail($uuid, trans('quiz.category'), 'message');
    }

    public function create(Request $request): QuizCategory
    {
        \DB::beginTransaction();

        $category = QuizCategory::forceCreate($this->formatParams($request));

        \DB::commit();

        return $category;
    }

    public function update(Request $request, QuizCategory $category): void
    {
        \DB::beginTransaction();

        $category->forceFill($this->formatParams($request, $category))->save();

        \DB::commit();
    }

    public function deletable(QuizCategory $category): void
    {
        $hasQuizzes = $category->quizzes()->exists();

        if ($hasQuizzes) {
            throw ValidationException::withMessages(['message' => trans('quiz.category.could_not_delete_category_with_quizzes')]);
        }

        $hasChildren = $category->children()->exists();

        if ($hasChildren) {
            throw ValidationException::withMessages(['message' => trans('quiz.category.could_not_delete_category_with_children')]);
        }
    }

    public function delete(QuizCategory $category): void
    {
        \DB::beginTransaction();

        $category->delete();

        \DB::commit();
    }

    private function formatParams(Request $request, ?QuizCategory $category = null): array
    {
        $formatted = [
            'name' => $request->name,
            'description' => $request->description,
            'parent_id' => $request->parent_id,
            'color' => $request->color,
            'icon' => $request->icon,
            'is_active' => $request->boolean('is_active', true),
            'meta' => $request->meta ?? [],
        ];

        if (!$category) {
            $formatted['team_id'] = auth()->user()?->current_team_id;
        }

        return $formatted;
    }
}
