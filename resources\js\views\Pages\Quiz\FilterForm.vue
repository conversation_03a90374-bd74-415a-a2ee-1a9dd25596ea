<template>
    <FilterForm
        :init-form="initForm"
        :form="form"
        :multiple="['categories']"
        @hide="emit('hide')"
    >
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div>
                <BaseSelect
                    name="categoryUuid"
                    :label="$trans('quiz.props.category')"
                    v-model="form.categoryUuid"
                    :options="preRequisites.categories"
                    clearable
                />
            </div>

            <div>
                <BaseSelect
                    name="difficulty"
                    :label="$trans('quiz.props.difficulty')"
                    v-model="form.difficulty"
                    :options="preRequisites.difficulties"
                    clearable
                />
            </div>

            <div>
                <BaseSelect
                    name="isPublic"
                    :label="$trans('quiz.props.visibility')"
                    v-model="form.isPublic"
                    :options="[
                        { label: 'Public', value: true },
                        { label: 'Private', value: false }
                    ]"
                    clearable
                />
            </div>

            <div>
                <BaseInput
                    name="createdAtStart"
                    type="date"
                    :label="$trans('global.created_from')"
                    v-model="form.createdAtStart"
                />
            </div>

            <div>
                <BaseInput
                    name="createdAtEnd"
                    type="date"
                    :label="$trans('global.created_to')"
                    v-model="form.createdAtEnd"
                />
            </div>

            <div>
                <BaseSelect
                    name="hasQuestions"
                    :label="$trans('quiz.has_questions')"
                    v-model="form.hasQuestions"
                    :options="[
                        { label: 'With Questions', value: true },
                        { label: 'Without Questions', value: false }
                    ]"
                    clearable
                />
            </div>
        </div>
    </FilterForm>
</template>

<script setup>
import { reactive, onMounted } from "vue"
import { useRoute } from "vue-router"

const route = useRoute()

const emit = defineEmits(["hide"])

const props = defineProps({
    preRequisites: {
        type: Object,
        default: () => ({}),
    },
})

const initForm = {
    categoryUuid: "",
    difficulty: "",
    isPublic: "",
    createdAtStart: "",
    createdAtEnd: "",
    hasQuestions: "",
}

const form = reactive({ ...initForm })

const fetchData = reactive({
    isLoaded: true,
})

onMounted(async () => {
    fetchData.isLoaded = true
})
</script>
