<template>
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <PageHeader>
            <template #title>
                <span>{{ $trans('quiz.attempt.result') }}</span>
            </template>
        </PageHeader>

        <!-- Loading State -->
        <div v-if="loading" class="text-center py-20">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p class="mt-4 text-gray-600">{{ $trans('quiz.loading_results') }}</p>
        </div>

        <!-- Results Content -->
        <div v-else-if="attempt.uuid" class="space-y-8">
            <!-- Score Overview -->
            <BaseCard>
                <template #title>
                    {{ $trans('quiz.attempt.score_overview') }}
                </template>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Score Display -->
                    <div class="text-center">
                        <div class="text-6xl font-bold mb-4" :class="getScoreColor(attempt.score)">
                            {{ attempt.score }}%
                        </div>
                        <div class="text-xl text-gray-700 mb-2">
                            {{ getScoreMessage(attempt.score) }}
                        </div>
                        <div class="text-gray-600">
                            {{ attempt.correctAnswers }} {{ $trans('quiz.out_of') }} {{ attempt.totalQuestions }} {{ $trans('quiz.questions_correct') }}
                        </div>
                        <div class="mt-4">
                            <span 
                                class="px-4 py-2 rounded-full text-sm font-medium"
                                :class="attempt.passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                            >
                                {{ attempt.passed ? $trans('quiz.passed') : $trans('quiz.failed') }}
                            </span>
                        </div>
                    </div>

                    <!-- Additional Stats -->
                    <div class="grid grid-cols-2 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-semibold text-blue-600">{{ formatTime(attempt.timeSpent) }}</div>
                            <div class="text-sm text-gray-600">{{ $trans('quiz.time_spent') }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-semibold text-purple-600">{{ attempt.attemptNumber }}</div>
                            <div class="text-sm text-gray-600">{{ $trans('quiz.attempt_number') }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-semibold text-green-600">{{ attempt.quiz?.passingScore || 0 }}%</div>
                            <div class="text-sm text-gray-600">{{ $trans('quiz.passing_score') }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-semibold text-orange-600">{{ attempt.quiz?.averageScore || 0 }}%</div>
                            <div class="text-sm text-gray-600">{{ $trans('quiz.quiz_average') }}</div>
                        </div>
                    </div>
                </div>
            </BaseCard>

            <!-- Quiz Information -->
            <BaseCard>
                <template #title>
                    {{ $trans('quiz.quiz_information') }}
                </template>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $trans('quiz.title') }}
                        </label>
                        <p class="text-gray-900">{{ attempt.quiz?.title }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $trans('quiz.category.category') }}
                        </label>
                        <p class="text-gray-900">{{ attempt.quiz?.category?.name || '-' }}</p>
                    </div>

                    <div v-if="attempt.quiz?.description">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $trans('quiz.description') }}
                        </label>
                        <p class="text-gray-900">{{ attempt.quiz.description }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $trans('quiz.difficulty') }}
                        </label>
                        <span 
                            class="px-2 py-1 text-xs font-medium rounded-full"
                            :class="getDifficultyClass(attempt.quiz?.difficulty)"
                        >
                            {{ $trans(`quiz.difficulty.${attempt.quiz?.difficulty}`) }}
                        </span>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $trans('quiz.attempt.started_at') }}
                        </label>
                        <p class="text-gray-900">{{ $cal.toUserTimezone(attempt.startedAt, 'datetime') }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $trans('quiz.attempt.completed_at') }}
                        </label>
                        <p class="text-gray-900">{{ $cal.toUserTimezone(attempt.completedAt, 'datetime') }}</p>
                    </div>
                </div>
            </BaseCard>

            <!-- Question Results -->
            <BaseCard v-if="questionResults.length > 0">
                <template #title>
                    {{ $trans('quiz.question_by_question_results') }}
                </template>
                
                <div class="space-y-6">
                    <div 
                        v-for="(questionResult, index) in questionResults" 
                        :key="questionResult.uuid"
                        class="border-b pb-6 last:border-b-0"
                    >
                        <!-- Question Header -->
                        <div class="flex justify-between items-start mb-4">
                            <h4 class="font-medium text-gray-900">
                                {{ $trans('quiz.question') }} {{ index + 1 }}
                            </h4>
                            <div class="flex items-center space-x-3">
                                <span 
                                    class="px-3 py-1 rounded-full text-sm font-medium"
                                    :class="questionResult.isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                >
                                    {{ questionResult.isCorrect ? $trans('quiz.correct') : $trans('quiz.incorrect') }}
                                </span>
                                <span class="text-sm text-gray-600">
                                    {{ questionResult.points || 0 }} {{ $trans('quiz.points') }}
                                </span>
                            </div>
                        </div>

                        <!-- Question Text -->
                        <div class="mb-4">
                            <p class="text-gray-700">{{ questionResult.question }}</p>
                            <div v-if="questionResult.image" class="mt-2">
                                <img 
                                    :src="questionResult.image" 
                                    :alt="$trans('quiz.question_image')"
                                    class="max-w-sm h-auto rounded-lg"
                                />
                            </div>
                        </div>

                        <!-- Multiple Choice Results -->
                        <div v-if="questionResult.type === 'multiple_choice'" class="space-y-2">
                            <div 
                                v-for="option in questionResult.options" 
                                :key="option.uuid"
                                class="flex items-center p-3 rounded border"
                                :class="getOptionClass(option, questionResult)"
                            >
                                <div class="flex items-center flex-1">
                                    <div 
                                        class="w-4 h-4 rounded-full border-2 mr-3"
                                        :class="option.uuid === questionResult.userAnswer ? 'border-blue-500 bg-blue-500' : 'border-gray-300'"
                                    ></div>
                                    <span>{{ option.text }}</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span v-if="option.isCorrect" class="text-green-600 text-sm">
                                        <i class="fas fa-check mr-1"></i>{{ $trans('quiz.correct_answer') }}
                                    </span>
                                    <span v-if="option.uuid === questionResult.userAnswer" class="text-blue-600 text-sm">
                                        <i class="fas fa-user mr-1"></i>{{ $trans('quiz.your_answer') }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- True/False Results -->
                        <div v-else-if="questionResult.type === 'true_false'" class="grid grid-cols-2 gap-4">
                            <div 
                                class="p-3 rounded border text-center"
                                :class="questionResult.correctAnswer === 'true' ? 'border-green-500 bg-green-50' : 'border-gray-300'"
                            >
                                <div class="flex items-center justify-center space-x-2">
                                    <span>{{ $trans('quiz.true') }}</span>
                                    <div class="flex items-center space-x-1">
                                        <span v-if="questionResult.correctAnswer === 'true'" class="text-green-600">
                                            <i class="fas fa-check"></i>
                                        </span>
                                        <span v-if="questionResult.userAnswer === 'true'" class="text-blue-600">
                                            <i class="fas fa-user"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div 
                                class="p-3 rounded border text-center"
                                :class="questionResult.correctAnswer === 'false' ? 'border-green-500 bg-green-50' : 'border-gray-300'"
                            >
                                <div class="flex items-center justify-center space-x-2">
                                    <span>{{ $trans('quiz.false') }}</span>
                                    <div class="flex items-center space-x-1">
                                        <span v-if="questionResult.correctAnswer === 'false'" class="text-green-600">
                                            <i class="fas fa-check"></i>
                                        </span>
                                        <span v-if="questionResult.userAnswer === 'false'" class="text-blue-600">
                                            <i class="fas fa-user"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Text Answer Results -->
                        <div v-else class="space-y-3">
                            <div>
                                <h5 class="font-medium text-gray-700 mb-2">{{ $trans('quiz.your_answer') }}:</h5>
                                <div class="p-3 bg-blue-50 rounded border">
                                    {{ questionResult.userAnswer || $trans('quiz.no_answer_provided') }}
                                </div>
                            </div>
                            <div v-if="questionResult.correctAnswer">
                                <h5 class="font-medium text-gray-700 mb-2">{{ $trans('quiz.correct_answer') }}:</h5>
                                <div class="p-3 bg-green-50 rounded border">
                                    {{ questionResult.correctAnswer }}
                                </div>
                            </div>
                            <div v-if="questionResult.explanation">
                                <h5 class="font-medium text-gray-700 mb-2">{{ $trans('quiz.explanation') }}:</h5>
                                <div class="p-3 bg-gray-50 rounded border">
                                    {{ questionResult.explanation }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </BaseCard>

            <!-- Action Buttons -->
            <div class="flex justify-between">
                <button
                    @click="router.push({ name: 'QuizAttemptList' })"
                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                    {{ $trans('global.back') }}
                </button>

                <div class="space-x-3">
                    <button
                        @click="printResults"
                        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                        <i class="fas fa-print mr-2"></i>
                        {{ $trans('quiz.print_results') }}
                    </button>
                    
                    <button
                        @click="exportResults"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                        <i class="fas fa-download mr-2"></i>
                        {{ $trans('quiz.export_results') }}
                    </button>

                    <button
                        v-if="canRetake"
                        @click="retakeQuiz"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                        {{ $trans('quiz.retake_quiz') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Not Found -->
        <div v-else class="text-center py-20">
            <div class="text-red-500 text-6xl mb-4">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">
                {{ $trans('quiz.attempt.not_found') }}
            </h3>
            <p class="text-gray-600">
                {{ $trans('quiz.attempt.invalid_attempt') }}
            </p>
        </div>
    </div>
</template>

<script>
export default {
    name: "QuizAttemptResult",
}
</script>

<script setup>
import { reactive, ref, computed, onMounted, inject } from "vue"
import { useRoute, useRouter } from "vue-router"

const route = useRoute()
const router = useRouter()
const $cal = inject("$cal")

const loading = ref(true)
const attempt = reactive({})
const questionResults = ref([])

const canRetake = computed(() => {
    return attempt.quiz?.allowRetakes && attempt.status === 'completed'
})

const fetchResults = async () => {
    try {
        loading.value = true
        // TODO: Implement API call to fetch attempt results
        console.log('Fetching attempt results for:', route.params.uuid)
    } catch (error) {
        console.error('Error fetching results:', error)
    } finally {
        loading.value = false
    }
}

const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
}

const getScoreMessage = (score) => {
    if (score >= 90) return 'Excellent!'
    if (score >= 80) return 'Great job!'
    if (score >= 70) return 'Good work!'
    if (score >= 60) return 'Not bad!'
    return 'Keep trying!'
}

const getDifficultyClass = (difficulty) => {
    switch (difficulty) {
        case 'easy':
            return 'bg-green-100 text-green-800'
        case 'medium':
            return 'bg-yellow-100 text-yellow-800'
        case 'hard':
            return 'bg-red-100 text-red-800'
        default:
            return 'bg-gray-100 text-gray-800'
    }
}

const getOptionClass = (option, questionResult) => {
    if (option.isCorrect) {
        return 'border-green-500 bg-green-50'
    }
    if (option.uuid === questionResult.userAnswer && !option.isCorrect) {
        return 'border-red-500 bg-red-50'
    }
    return 'border-gray-300 bg-gray-50'
}

const formatTime = (seconds) => {
    if (!seconds) return '0:00'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// Removed custom formatDate - using $cal.toUserTimezone instead

const printResults = () => {
    window.print()
}

const exportResults = () => {
    // TODO: Implement export functionality
    console.log('Export results')
}

const retakeQuiz = () => {
    router.push({
        name: 'QuizTake',
        params: { uuid: attempt.quiz.uuid }
    })
}

onMounted(() => {
    fetchResults()
})
</script>
