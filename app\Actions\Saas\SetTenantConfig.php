<?php

namespace App\Actions\Saas;

use App\Helpers\CalHelper;
use App\Models\Saas\Tenant;
use Closure;

class SetTenantConfig
{
    public function handle(Tenant $tenant, Closure $next)
    {
        $host = $_SERVER['HTTP_HOST'];

        config([
            'cache.prefix' => $tenant->domain,
            'cache.stores.file.path' => storage_path('framework/cache/data/'.$tenant->domain),
        ]);
        app('cache')->forgetDriver(config('cache.default'));

        $permissionRegistrar = app(\Spatie\Permission\PermissionRegistrar::class);
        $permissionRegistrar->cacheKey = 'spatie.permission.cache.tenant.'.$tenant->domain;

        $expiryDate = config('saas.tenant.expiry_date');
        $expireInDays = null;
        $isExpired = false;

        if ($expiryDate && $expiryDate >= today()->toDateString()) {
            $expireInDays = CalHelper::dateDiff(today()->toDateString(), $expiryDate);
        } elseif ($expiryDate && $expiryDate < today()->toDateString()) {
            $expireInDays = -1;
        }

        if ($expiryDate && $expiryDate < today()->toDateString()) {
            $isExpired = true;
        }

        if (config('saas.plan.features.is_free', false)) {
            $isExpired = false;
        }

        $expireAlert = null;
        $alertDays = config('saas.landlord.tenant.subscription_end_alert', 7);
        $showExpiryAlert = false;

        if ($isExpired || ($expiryDate && $expireInDays < $alertDays)) {
            $showExpiryAlert = true;
        }

        $validity = [
            'is_trial' => config('saas.tenant.is_trial'),
            'expiry_date' => \Cal::date($expiryDate),
            'is_expired' => $isExpired,
            'redirect_on_expiration' => $isExpired ? true : false,
            'expire_in_days' => $expireInDays,
            'show_expiry_alert' => $showExpiryAlert,
            'expire_alert' => $expireAlert,
        ];

        $defaultModules = ['academic', 'student', 'employee', 'guardian', 'contact', 'ai', 'quiz'];
        $availableModules = config('saas.plan.features.modules', []);

        $availableModules = array_merge($defaultModules, $availableModules);

        config([
            'app.url' => 'https://'.$host,
            'filesystems.disks.public.url' => 'https://'.$host.'/storage',
            'session.domain' => null,
            'sanctum.stateful' => [$host],
            'saas.available_modules' => $availableModules,
            'saas.validity' => $validity,
        ]);

        if ($tenant->domain == 'demo' && ! config('saas.status')) {
            config([
                'app.mode' => 'test',
            ]);
        }

        return $next($tenant);
    }
}
