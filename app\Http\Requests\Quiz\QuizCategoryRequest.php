<?php

namespace App\Http\Requests\Quiz;

use App\Http\Requests\BaseRequest;
use App\Models\Quiz\QuizCategory;
use Illuminate\Validation\Rule;

class QuizCategoryRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $category = $this->route('category');
        $isUpdate = $category instanceof QuizCategory || is_string($category);

        return [
            'name' => [
                'required',
                'string',
                'max:100',
                Rule::unique('quiz_categories', 'name')
                    ->where('team_id', auth()->user()->current_team_id)
                    ->ignore($isUpdate && $category instanceof QuizCategory ? $category->id : null),
            ],
            'description' => 'nullable|string|max:1000',
            'parent_id' => [
                'nullable',
                'exists:quiz_categories,id',
                function ($attribute, $value, $fail) use ($category) {
                    if ($value && $category instanceof QuizCategory && $value == $category->id) {
                        $fail(trans('quiz.category.validation.cannot_be_parent_of_itself'));
                    }
                },
            ],
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:50',
            'is_active' => 'boolean',
            'meta' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => trans('validation.required', ['attribute' => trans('quiz.category.props.name')]),
            'name.unique' => trans('validation.unique', ['attribute' => trans('quiz.category.props.name')]),
            'name.max' => trans('validation.max.string', ['attribute' => trans('quiz.category.props.name'), 'max' => 100]),
            'description.max' => trans('validation.max.string', ['attribute' => trans('quiz.category.props.description'), 'max' => 1000]),
            'parent_id.exists' => trans('validation.exists', ['attribute' => trans('quiz.category.props.parent')]),
            'color.regex' => trans('validation.regex', ['attribute' => trans('quiz.category.props.color')]),
            'color.max' => trans('validation.max.string', ['attribute' => trans('quiz.category.props.color'), 'max' => 7]),
            'icon.max' => trans('validation.max.string', ['attribute' => trans('quiz.category.props.icon'), 'max' => 50]),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => trans('quiz.category.props.name'),
            'description' => trans('quiz.category.props.description'),
            'parent_id' => trans('quiz.category.props.parent'),
            'color' => trans('quiz.category.props.color'),
            'icon' => trans('quiz.category.props.icon'),
            'is_active' => trans('quiz.category.props.is_active'),
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate parent category hierarchy to prevent circular references
            if ($this->has('parent_id') && $this->input('parent_id')) {
                $category = $this->route('category');
                if ($category instanceof QuizCategory) {
                    $this->validateParentHierarchy($validator, $category, $this->input('parent_id'));
                }
            }
        });
    }

    /**
     * Validate parent category hierarchy to prevent circular references.
     */
    private function validateParentHierarchy($validator, QuizCategory $category, int $parentId): void
    {
        $currentParent = QuizCategory::find($parentId);
        $visited = [$category->id];

        while ($currentParent) {
            if (in_array($currentParent->id, $visited)) {
                $validator->errors()->add(
                    'parent_id',
                    trans('quiz.category.validation.circular_reference_detected')
                );
                break;
            }

            $visited[] = $currentParent->id;
            $currentParent = $currentParent->parent;
        }
    }
}
