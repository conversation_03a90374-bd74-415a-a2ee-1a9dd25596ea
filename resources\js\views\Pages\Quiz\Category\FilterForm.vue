<template>
    <FilterForm
        :init-form="initForm"
        :form="form"
        :multiple="[]"
        @hide="emit('hide')"
    >
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Status Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('global.status') }}
                </label>
                <select
                    v-model="form.isActive"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="1">{{ $trans('global.active') }}</option>
                    <option value="0">{{ $trans('global.inactive') }}</option>
                </select>
            </div>

            <!-- Visibility Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.category.visibility') }}
                </label>
                <select
                    v-model="form.visibility"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="public">{{ $trans('quiz.visibility.public') }}</option>
                    <option value="private">{{ $trans('quiz.visibility.private') }}</option>
                    <option value="restricted">{{ $trans('quiz.visibility.restricted') }}</option>
                </select>
            </div>

            <!-- Quiz Count Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.category.quiz_count') }}
                </label>
                <select
                    v-model="form.quizCount"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="0">{{ $trans('quiz.category.empty_categories') }}</option>
                    <option value="1-5">1-5 {{ $trans('quiz.quizzes') }}</option>
                    <option value="6-10">6-10 {{ $trans('quiz.quizzes') }}</option>
                    <option value="11-20">11-20 {{ $trans('quiz.quizzes') }}</option>
                    <option value="20+">20+ {{ $trans('quiz.quizzes') }}</option>
                </select>
            </div>

            <!-- Created By Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.category.created_by') }}
                </label>
                <select
                    v-model="form.createdBy"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="">{{ $trans('global.all') }}</option>
                    <option value="me">{{ $trans('quiz.category.my_categories') }}</option>
                    <option 
                        v-for="user in availableUsers" 
                        :key="user.uuid" 
                        :value="user.uuid"
                    >
                        {{ user.name }}
                    </option>
                </select>
            </div>

            <!-- Date Range Filters -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.category.created_from') }}
                </label>
                <input
                    v-model="form.createdFrom"
                    type="date"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('quiz.category.created_to') }}
                </label>
                <input
                    v-model="form.createdTo"
                    type="date"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
            </div>

            <!-- Search Filter -->
            <div class="md:col-span-2 lg:col-span-3">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ $trans('global.search') }}
                </label>
                <input
                    v-model="form.search"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="$trans('quiz.category.search_categories')"
                />
            </div>
        </div>
    </FilterForm>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue"
import { useRoute } from "vue-router"

const route = useRoute()

const emit = defineEmits(["hide"])

const availableUsers = ref([])

const initForm = {
    isActive: "",
    visibility: "",
    quizCount: "",
    createdBy: "",
    createdFrom: "",
    createdTo: "",
    search: "",
}

const form = reactive({ ...initForm })

const fetchData = reactive({
    isLoaded: true,
})

onMounted(async () => {
    fetchData.isLoaded = true
})
</script>
