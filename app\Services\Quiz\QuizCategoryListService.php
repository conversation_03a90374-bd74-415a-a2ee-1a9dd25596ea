<?php

namespace App\Services\Quiz;

use App\Contracts\ListGenerator;
use App\Http\Resources\Quiz\QuizCategoryResource;
use App\Models\Quiz\QuizCategory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class QuizCategoryListService extends ListGenerator
{
    protected $allowedSorts = ['created_at', 'name'];

    protected $defaultSort = 'name';

    protected $defaultOrder = 'asc';

    public function getHeaders(): array
    {
        $headers = [
            [
                'key' => 'name',
                'label' => trans('quiz.category.props.name'),
                'sortable' => true,
                'visibility' => true,
            ],
            [
                'key' => 'description',
                'label' => trans('quiz.category.props.description'),
                'sortable' => false,
                'visibility' => true,
            ],
            [
                'key' => 'parent',
                'label' => trans('quiz.category.props.parent'),
                'print_label' => 'parent.name',
                'sortable' => false,
                'visibility' => true,
            ],
            [
                'key' => 'quizzesCount',
                'label' => trans('quiz.category.props.quizzes_count'),
                'sortable' => false,
                'visibility' => true,
            ],
            [
                'key' => 'isActive',
                'label' => trans('quiz.category.props.is_active'),
                'print_label' => 'is_active',
                'sortable' => false,
                'visibility' => true,
            ],
            [
                'key' => 'createdAt',
                'label' => trans('general.created_at'),
                'print_label' => 'created_at.formatted',
                'sortable' => true,
                'visibility' => true,
            ],
        ];

        if (request()->ajax()) {
            $headers[] = $this->actionHeader;
        }

        return $headers;
    }

    public function filter(Request $request): Builder
    {
        return QuizCategory::query()
            ->with(['parent', 'children'])
            ->withCount('quizzes')
            ->byTeam()
            ->when($request->query('parent'), function ($q, $parent) {
                if ($parent === 'root') {
                    $q->rootCategories();
                } else {
                    $q->whereHas('parent', function ($q) use ($parent) {
                        $q->where('uuid', $parent);
                    });
                }
            })
            ->when($request->query('active') !== null, function ($q) use ($request) {
                if ($request->boolean('active')) {
                    $q->active();
                } else {
                    $q->where('is_active', false);
                }
            })
            ->filter([
                'App\QueryFilters\LikeMatch:name,description',
                'App\QueryFilters\UuidMatch',
            ]);
    }

    public function paginate(Request $request): AnonymousResourceCollection
    {
        return QuizCategoryResource::collection($this->filter($request)
            ->orderBy($this->getSort(), $this->getOrder())
            ->paginate((int) $this->getPageLength(), ['*'], 'current_page'))
            ->additional([
                'headers' => $this->getHeaders(),
                'meta' => [
                    'allowed_sorts' => $this->allowedSorts,
                    'default_sort' => $this->defaultSort,
                    'default_order' => $this->defaultOrder,
                ],
            ]);
    }

    public function list(Request $request): AnonymousResourceCollection
    {
        return $this->paginate($request);
    }
}
