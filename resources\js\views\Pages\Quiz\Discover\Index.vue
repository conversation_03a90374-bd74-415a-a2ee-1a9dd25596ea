<template>
    <div class="space-y-6">
        <!-- Header -->
        <PageHeader>
            <template #title>
                <span>{{ $trans('quiz.discovery.discover_quizzes') }}</span>
            </template>
        </PageHeader>

        <!-- Search and Filters -->
        <BaseCard>
            <div class="space-y-4">
                <!-- Search Bar -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input
                        v-model="searchQuery"
                        type="text"
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        :placeholder="$trans('quiz.discovery.search_quizzes')"
                        @input="searchQuizzes"
                    />
                </div>

                <!-- Filter Tabs -->
                <div class="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                    <button
                        v-for="tab in filterTabs"
                        :key="tab.key"
                        @click="activeTab = tab.key"
                        class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors"
                        :class="activeTab === tab.key 
                            ? 'bg-white text-blue-600 shadow-sm' 
                            : 'text-gray-600 hover:text-gray-900'"
                    >
                        <i :class="tab.icon" class="mr-2"></i>
                        {{ $trans(tab.label) }}
                    </button>
                </div>

                <!-- Category Filter -->
                <div class="flex flex-wrap gap-2">
                    <button
                        v-for="category in categories"
                        :key="category.uuid"
                        @click="toggleCategory(category.uuid)"
                        class="px-3 py-1 text-sm rounded-full border transition-colors"
                        :class="selectedCategories.includes(category.uuid)
                            ? 'bg-blue-100 text-blue-800 border-blue-300'
                            : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'"
                    >
                        {{ category.name }}
                    </button>
                </div>
            </div>
        </BaseCard>

        <!-- Loading State -->
        <div v-if="loading" class="text-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p class="mt-4 text-gray-600">{{ $trans('quiz.discovery.loading_quizzes') }}</p>
        </div>

        <!-- Quiz Grid -->
        <div v-else-if="filteredQuizzes.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div 
                v-for="quiz in filteredQuizzes" 
                :key="quiz.uuid"
                class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
                @click="viewQuiz(quiz)"
            >
                <!-- Quiz Image -->
                <div class="h-48 bg-gradient-to-br from-blue-400 to-purple-500 rounded-t-lg relative">
                    <img 
                        v-if="quiz.image" 
                        :src="quiz.image" 
                        :alt="quiz.title"
                        class="w-full h-full object-cover rounded-t-lg"
                    />
                    <div class="absolute top-3 right-3">
                        <span 
                            class="px-2 py-1 text-xs font-medium rounded-full bg-white bg-opacity-90"
                            :class="getDifficultyClass(quiz.difficulty)"
                        >
                            {{ $trans(`quiz.difficulty.${quiz.difficulty}`) }}
                        </span>
                    </div>
                    <div class="absolute bottom-3 left-3 text-white">
                        <div class="flex items-center space-x-3 text-sm">
                            <span>
                                <i class="fas fa-question-circle mr-1"></i>
                                {{ quiz.questionCount }}
                            </span>
                            <span v-if="quiz.timeLimit">
                                <i class="fas fa-clock mr-1"></i>
                                {{ quiz.timeLimit }}m
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Quiz Content -->
                <div class="p-4">
                    <div class="flex items-start justify-between mb-2">
                        <h3 class="font-semibold text-gray-900 line-clamp-2">{{ quiz.title }}</h3>
                        <button
                            @click.stop="toggleFavorite(quiz)"
                            class="text-gray-400 hover:text-red-500 transition-colors"
                        >
                            <i :class="quiz.isFavorited ? 'fas fa-heart text-red-500' : 'far fa-heart'"></i>
                        </button>
                    </div>

                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ quiz.description }}</p>

                    <!-- Quiz Stats -->
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                        <div class="flex items-center space-x-3">
                            <span>
                                <i class="fas fa-play mr-1"></i>
                                {{ quiz.attemptCount }} {{ $trans('quiz.attempts') }}
                            </span>
                            <span>
                                <i class="fas fa-star mr-1"></i>
                                {{ quiz.averageRating || 0 }}
                            </span>
                        </div>
                        <span class="text-xs">{{ $cal.toUserTimezone(quiz.createdAt, 'date') }}</span>
                    </div>

                    <!-- Creator Info -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img 
                                v-if="quiz.creator?.avatar" 
                                :src="quiz.creator.avatar" 
                                :alt="quiz.creator.name"
                                class="w-6 h-6 rounded-full mr-2"
                            />
                            <div v-else class="w-6 h-6 bg-gray-300 rounded-full mr-2 flex items-center justify-center">
                                <i class="fas fa-user text-gray-600 text-xs"></i>
                            </div>
                            <span class="text-sm text-gray-700">{{ quiz.creator?.name }}</span>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center space-x-2">
                            <button
                                @click.stop="previewQuiz(quiz)"
                                class="text-blue-600 hover:text-blue-800 text-sm"
                                :title="$trans('quiz.preview')"
                            >
                                <i class="fas fa-eye"></i>
                            </button>
                            <button
                                @click.stop="startQuiz(quiz)"
                                class="text-green-600 hover:text-green-800 text-sm"
                                :title="$trans('quiz.start_quiz')"
                            >
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div v-else class="text-center py-12">
            <div class="text-gray-400 text-6xl mb-4">
                <i class="fas fa-search"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">
                {{ $trans('quiz.discovery.no_quizzes_found') }}
            </h3>
            <p class="text-gray-600">
                {{ $trans('quiz.discovery.try_different_search') }}
            </p>
        </div>

        <!-- Load More -->
        <div v-if="hasMore && !loading" class="text-center">
            <button
                @click="loadMore"
                class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
                {{ $trans('quiz.discovery.load_more') }}
            </button>
        </div>
    </div>
</template>

<script>
export default {
    name: "QuizDiscover",
}
</script>

<script setup>
import { ref, reactive, computed, onMounted, inject } from "vue"
import { useRouter } from "vue-router"

const router = useRouter()
const $cal = inject("$cal")

const loading = ref(true)
const searchQuery = ref("")
const activeTab = ref("all")
const selectedCategories = ref([])
const quizzes = ref([])
const categories = ref([])
const hasMore = ref(true)
const currentPage = ref(1)

const filterTabs = [
    { key: "all", label: "quiz.discovery.all_quizzes", icon: "fas fa-th" },
    { key: "popular", label: "quiz.discovery.popular", icon: "fas fa-fire" },
    { key: "recent", label: "quiz.discovery.recent", icon: "fas fa-clock" },
    { key: "featured", label: "quiz.discovery.featured", icon: "fas fa-star" },
    { key: "collections", label: "quiz.discovery.collections", icon: "fas fa-folder" },
]

const filteredQuizzes = computed(() => {
    let filtered = quizzes.value

    // Filter by search query
    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(quiz => 
            quiz.title.toLowerCase().includes(query) ||
            quiz.description.toLowerCase().includes(query)
        )
    }

    // Filter by categories
    if (selectedCategories.value.length > 0) {
        filtered = filtered.filter(quiz => 
            selectedCategories.value.includes(quiz.categoryId)
        )
    }

    // Filter by active tab
    switch (activeTab.value) {
        case "popular":
            filtered = filtered.filter(quiz => quiz.attemptCount > 10)
            break
        case "recent":
            filtered = filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            break
        case "featured":
            filtered = filtered.filter(quiz => quiz.featured)
            break
        case "collections":
            filtered = filtered.filter(quiz => quiz.type === 'collection')
            break
    }

    return filtered
})

const fetchQuizzes = async () => {
    try {
        loading.value = true
        // TODO: Implement API call to fetch public quizzes
        console.log('Fetching public quizzes')
    } catch (error) {
        console.error('Error fetching quizzes:', error)
    } finally {
        loading.value = false
    }
}

const fetchCategories = async () => {
    try {
        // TODO: Implement API call to fetch categories
        console.log('Fetching categories')
    } catch (error) {
        console.error('Error fetching categories:', error)
    }
}

const searchQuizzes = () => {
    // Debounce search
    // TODO: Implement search API call
    console.log('Searching quizzes:', searchQuery.value)
}

const toggleCategory = (categoryId) => {
    const index = selectedCategories.value.indexOf(categoryId)
    if (index > -1) {
        selectedCategories.value.splice(index, 1)
    } else {
        selectedCategories.value.push(categoryId)
    }
}

const getDifficultyClass = (difficulty) => {
    switch (difficulty) {
        case 'easy':
            return 'text-green-700'
        case 'medium':
            return 'text-yellow-700'
        case 'hard':
            return 'text-red-700'
        default:
            return 'text-gray-700'
    }
}

// Removed custom formatDate - using $cal.toUserTimezone instead

const viewQuiz = (quiz) => {
    router.push({
        name: 'QuizShow',
        params: { uuid: quiz.uuid }
    })
}

const previewQuiz = (quiz) => {
    router.push({
        name: 'QuizPreview',
        params: { uuid: quiz.uuid }
    })
}

const startQuiz = (quiz) => {
    router.push({
        name: 'QuizTake',
        params: { uuid: quiz.uuid }
    })
}

const toggleFavorite = (quiz) => {
    // TODO: Implement favorite toggle API call
    quiz.isFavorited = !quiz.isFavorited
    console.log('Toggle favorite:', quiz)
}

const loadMore = () => {
    currentPage.value++
    // TODO: Implement load more API call
    console.log('Load more quizzes, page:', currentPage.value)
}

onMounted(() => {
    fetchQuizzes()
    fetchCategories()
})
</script>
