<template>
    <ListItem
        :init-url="initUrl"
        :pre-requisites="true"
        @setPreRequisites="setPreRequisites"
        @setItems="setItems"
    >
        <template #header>
            <PageHeader
                :title="$trans('quiz.quiz')"
                :navs="[
                    { label: $trans('quiz.quiz'), path: 'Quiz' },
                ]"
            >
                <PageHeaderAction
                    url="quiz/"
                    name="Quiz"
                    :title="$trans('quiz.quiz')"
                    :actions="userActions"
                    :dropdown-actions="dropdownActions"
                    @toggleFilter="showFilter = !showFilter"
                />
            </PageHeader>
        </template>

        <template #filter>
            <ParentTransition appear :visibility="showFilter">
                <FilterForm
                    @refresh="emitter.emit('listItems')"
                    :pre-requisites="preRequisites"
                    @hide="showFilter = false"
                />
            </ParentTransition>
        </template>

        <ParentTransition appear :visibility="true">
            <DataTable
                :header="quizzes.headers"
                :meta="quizzes.meta"
                module="quiz"
                @refresh="emitter.emit('listItems')"
            >
                <DataRow
                    v-for="quiz in quizzes.data"
                    :key="quiz.uuid"
                    @double-click="
                        router.push({
                            name: 'QuizShow',
                            params: { uuid: quiz.uuid },
                        })
                    "
                >
                    <DataCell name="title">
                        {{ quiz.title }}
                        <TextMuted block v-if="quiz.description">
                            {{ quiz.description }}
                        </TextMuted>
                        <div class="flex items-center gap-2 mt-1">
                            <BaseBadge
                                v-if="quiz.category"
                                design="info"
                                size="sm"
                            >
                                {{ quiz.category.name }}
                            </BaseBadge>
                            <BaseBadge
                                :design="quiz.isPublished ? 'success' : 'warning'"
                                size="sm"
                            >
                                {{ quiz.isPublished ? $trans('quiz.published') : $trans('quiz.draft') }}
                            </BaseBadge>
                            <BaseBadge
                                v-if="quiz.isShared"
                                design="primary"
                                size="sm"
                            >
                                {{ $trans('quiz.shared') }}
                            </BaseBadge>
                        </div>
                    </DataCell>
                    <DataCell name="questions">
                        {{ quiz.questionsCount || 0 }}
                        <TextMuted block>
                            {{ $trans('quiz.questions') }}
                        </TextMuted>
                    </DataCell>
                    <DataCell name="difficulty">
                        <BaseBadge
                            :design="getDifficultyDesign(quiz.difficulty)"
                            size="sm"
                        >
                            {{ $trans(`quiz.difficulty.${quiz.difficulty}`) }}
                        </BaseBadge>
                    </DataCell>
                    <DataCell name="attempts">
                        {{ quiz.attemptsCount || 0 }}
                        <TextMuted block>
                            {{ $trans('quiz.attempts') }}
                        </TextMuted>
                    </DataCell>
                    <DataCell name="timeLimit">
                        <span v-if="quiz.timeLimit">
                            {{ quiz.timeLimit }} {{ $trans('quiz.minutes') }}
                        </span>
                        <TextMuted v-else>
                            {{ $trans('quiz.no_time_limit') }}
                        </TextMuted>
                    </DataCell>
                    <DataCell name="createdBy">
                        {{ quiz.createdBy?.name }}
                        <TextMuted block>
                            {{ $cal.toUserTimezone(quiz.createdAt, 'date') }}
                        </TextMuted>
                    </DataCell>
                    <DataCell name="actions">
                        <div class="flex items-center gap-2">
                            <BaseButton
                                v-if="quiz.canTake"
                                size="sm"
                                design="success"
                                @click="takeQuiz(quiz)"
                            >
                                {{ $trans('quiz.take_quiz') }}
                            </BaseButton>
                            <BaseButton
                                v-if="quiz.canEdit"
                                size="sm"
                                design="primary"
                                @click="editQuiz(quiz)"
                            >
                                {{ $trans('global.edit') }}
                            </BaseButton>
                            <BaseButton
                                v-if="quiz.canView"
                                size="sm"
                                design="info"
                                @click="viewQuiz(quiz)"
                            >
                                {{ $trans('global.view') }}
                            </BaseButton>
                            <BaseDropdown>
                                <template #trigger>
                                    <BaseButton size="sm" design="light">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </BaseButton>
                                </template>
                                <BaseDropdownItem
                                    v-if="quiz.canDuplicate"
                                    @click="duplicateQuiz(quiz)"
                                >
                                    <i class="fas fa-copy mr-2"></i>
                                    {{ $trans('global.duplicate') }}
                                </BaseDropdownItem>
                                <BaseDropdownItem
                                    v-if="quiz.canShare && !quiz.isShared"
                                    @click="shareQuiz(quiz)"
                                >
                                    <i class="fas fa-share mr-2"></i>
                                    {{ $trans('quiz.share') }}
                                </BaseDropdownItem>
                                <BaseDropdownItem
                                    v-if="quiz.canShare && quiz.isShared"
                                    @click="revokeSharing(quiz)"
                                >
                                    <i class="fas fa-ban mr-2"></i>
                                    {{ $trans('quiz.revoke_sharing') }}
                                </BaseDropdownItem>
                                <BaseDropdownItem
                                    v-if="quiz.canPublish && !quiz.isPublished"
                                    @click="publishQuiz(quiz)"
                                >
                                    <i class="fas fa-eye mr-2"></i>
                                    {{ $trans('quiz.publish') }}
                                </BaseDropdownItem>
                                <BaseDropdownItem
                                    v-if="quiz.canPublish && quiz.isPublished"
                                    @click="unpublishQuiz(quiz)"
                                >
                                    <i class="fas fa-eye-slash mr-2"></i>
                                    {{ $trans('quiz.unpublish') }}
                                </BaseDropdownItem>
                                <BaseDropdownItem
                                    v-if="quiz.canViewResults"
                                    @click="viewResults(quiz)"
                                >
                                    <i class="fas fa-chart-bar mr-2"></i>
                                    {{ $trans('quiz.view_results') }}
                                </BaseDropdownItem>
                                <BaseDropdownItem
                                    v-if="quiz.canDelete"
                                    @click="deleteQuiz(quiz)"
                                    class="text-red-600"
                                >
                                    <i class="fas fa-trash mr-2"></i>
                                    {{ $trans('global.delete') }}
                                </BaseDropdownItem>
                            </BaseDropdown>
                        </div>
                    </DataCell>
                </DataRow>
                <template #actionButton>
                    <BaseButton @click="router.push({ name: 'QuizCreate' })">
                        {{
                            $trans("global.add", {
                                attribute: $trans("quiz.quiz"),
                            })
                        }}
                    </BaseButton>
                </template>
            </DataTable>
        </ParentTransition>
    </ListItem>
</template>

<script>
export default {
    name: "QuizList",
}
</script>

<script setup>
import { ref, reactive, inject } from "vue"
import { useRouter } from "vue-router"
import { useStore } from "vuex"
import { useToast } from "vue-toastification"
import { perform } from "@core/helpers/action"
// Removed incorrect import - using $cal injection instead
import FilterForm from "./Filter.vue"

const router = useRouter()
const store = useStore()
const toast = useToast()
const emitter = inject("emitter")
const $cal = inject("$cal")

let userActions = ["create", "filter"]
let dropdownActions = ["print", "pdf", "excel"]

const initUrl = "quiz/"
const showFilter = ref(false)

const preRequisites = reactive({
    categories: [],
    difficulties: [],
    questionTypes: [],
})

const quizzes = reactive({})

const setItems = (data) => {
    Object.assign(quizzes, data)
}

const setPreRequisites = (data) => {
    Object.assign(preRequisites, data)
}

const getDifficultyDesign = (difficulty) => {
    const designs = {
        basic: 'success',
        intermediate: 'warning',
        advanced: 'danger'
    }
    return designs[difficulty] || 'info'
}

const takeQuiz = (quiz) => {
    router.push({
        name: 'QuizTake',
        params: { uuid: quiz.uuid }
    })
}

const editQuiz = (quiz) => {
    router.push({
        name: 'QuizEdit',
        params: { uuid: quiz.uuid }
    })
}

const viewQuiz = (quiz) => {
    router.push({
        name: 'QuizShow',
        params: { uuid: quiz.uuid }
    })
}

const duplicateQuiz = async (quiz) => {
    try {
        const response = await store.dispatch('quiz/duplicate', quiz.uuid)
        toast.success(response.message)
        emitter.emit('listItems')
    } catch (error) {
        // Error handled by store
    }
}

const shareQuiz = async (quiz) => {
    try {
        const response = await store.dispatch('quiz/generateShareLink', quiz.uuid)
        toast.success(response.message)
        emitter.emit('listItems')
    } catch (error) {
        // Error handled by store
    }
}

const revokeSharing = async (quiz) => {
    try {
        const response = await store.dispatch('quiz/revokeSharing', quiz.uuid)
        toast.success(response.message)
        emitter.emit('listItems')
    } catch (error) {
        // Error handled by store
    }
}

const publishQuiz = async (quiz) => {
    try {
        const response = await store.dispatch('quiz/publish', quiz.uuid)
        toast.success(response.message)
        emitter.emit('listItems')
    } catch (error) {
        // Error handled by store
    }
}

const unpublishQuiz = async (quiz) => {
    try {
        const response = await store.dispatch('quiz/unpublish', quiz.uuid)
        toast.success(response.message)
        emitter.emit('listItems')
    } catch (error) {
        // Error handled by store
    }
}

const viewResults = (quiz) => {
    router.push({
        name: 'QuizResults',
        params: { uuid: quiz.uuid }
    })
}

const deleteQuiz = async (quiz) => {
    if (await perform(quiz.uuid, 'quiz/', 'delete')) {
        emitter.emit('listItems')
    }
}
</script>
